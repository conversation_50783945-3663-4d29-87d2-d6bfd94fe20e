#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云OSS配置文件
请根据您的实际情况修改配置信息
"""

# 阿里云OSS配置
OSS_CONFIG = {
    # AccessKey信息 (在阿里云控制台获取)
    'access_key_id': 'YOUR_ACCESS_KEY_ID',           # 替换为您的AccessKey ID
    'access_key_secret': 'YOUR_ACCESS_KEY_SECRET',   # 替换为您的AccessKey Secret
    
    # OSS地域节点 (选择离您最近的地域)
    'endpoint': 'https://oss-cn-hangzhou.aliyuncs.com',  # 杭州节点
    # 其他常用节点:
    # 'endpoint': 'https://oss-cn-beijing.aliyuncs.com',   # 北京节点
    # 'endpoint': 'https://oss-cn-shanghai.aliyuncs.com',  # 上海节点
    # 'endpoint': 'https://oss-cn-shenzhen.aliyuncs.com',  # 深圳节点
    
    # Bucket名称 (需要先在OSS控制台创建)
    'bucket_name': 'your-bucket-name',               # 替换为您的Bucket名称
    
    # 文件存储路径前缀
    'file_prefix': 'charts/',                        # 图表文件存储目录
    
    # 是否启用OSS上传
    'enabled': False,                                # 设置为True启用OSS上传
}

def get_oss_config():
    """获取OSS配置"""
    return OSS_CONFIG

def is_oss_configured():
    """检查OSS是否已正确配置"""
    config = get_oss_config()
    
    # 检查必要配置是否已设置
    if not config.get('enabled', False):
        return False, "OSS上传未启用"
    
    if config.get('access_key_id') == 'YOUR_ACCESS_KEY_ID':
        return False, "AccessKey ID未设置"
    
    if config.get('access_key_secret') == 'YOUR_ACCESS_KEY_SECRET':
        return False, "AccessKey Secret未设置"
    
    if config.get('bucket_name') == 'your-bucket-name':
        return False, "Bucket名称未设置"
    
    if not config.get('endpoint'):
        return False, "OSS节点未设置"
    
    return True, "OSS配置正确"

def validate_oss_connection():
    """验证OSS连接"""
    try:
        import oss2
    except ImportError:
        return False, "缺少oss2库，请安装: pip install oss2"
    
    is_configured, message = is_oss_configured()
    if not is_configured:
        return False, message
    
    try:
        config = get_oss_config()
        auth = oss2.Auth(config['access_key_id'], config['access_key_secret'])
        bucket = oss2.Bucket(auth, config['endpoint'], config['bucket_name'])
        
        # 测试连接 (列出Bucket信息)
        bucket_info = bucket.get_bucket_info()
        return True, f"OSS连接成功，Bucket: {bucket_info.name}"
        
    except Exception as e:
        return False, f"OSS连接失败: {str(e)}"

# 配置说明
CONFIG_HELP = """
阿里云OSS配置说明:

1. 获取AccessKey:
   - 登录阿里云控制台
   - 进入"访问控制" -> "用户" -> "创建用户"
   - 或使用现有用户，点击"创建AccessKey"
   - 复制AccessKey ID和AccessKey Secret

2. 创建OSS Bucket:
   - 登录阿里云控制台
   - 进入"对象存储OSS"
   - 点击"创建Bucket"
   - 设置Bucket名称和地域
   - 读写权限设置为"公共读"

3. 选择地域节点:
   - 华东1(杭州): oss-cn-hangzhou.aliyuncs.com
   - 华北2(北京): oss-cn-beijing.aliyuncs.com
   - 华东2(上海): oss-cn-shanghai.aliyuncs.com
   - 华南1(深圳): oss-cn-shenzhen.aliyuncs.com

4. 修改配置:
   - 编辑 oss_config.py 文件
   - 填入正确的AccessKey信息
   - 设置正确的endpoint和bucket_name
   - 将enabled设置为True

5. 安装依赖:
   pip install oss2

6. 测试配置:
   python test_oss_config.py
"""

if __name__ == "__main__":
    print("阿里云OSS配置检查:")
    print("=" * 50)
    
    # 检查配置
    is_configured, message = is_oss_configured()
    print(f"配置状态: {message}")
    
    if is_configured:
        # 验证连接
        is_connected, conn_message = validate_oss_connection()
        print(f"连接状态: {conn_message}")
        
        if is_connected:
            print("✅ OSS配置完成，可以正常使用")
        else:
            print("❌ OSS连接失败，请检查配置")
    else:
        print("❌ OSS配置不完整")
        print("\n" + CONFIG_HELP)
