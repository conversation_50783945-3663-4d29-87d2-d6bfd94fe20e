{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>🎯 重点关注告警配置</span>
                <a href="/" class="btn btn-secondary btn-sm">返回告警列表</a>
            </div>
            <div class="card-body">
                <form id="focus-config-form">
                    <!-- 功能开关 -->
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enabled" {% if config.focus_alarms.enabled %}checked{% endif %}>
                            <label class="form-check-label" for="enabled">
                                <strong>启用重点关注告警功能</strong>
                            </label>
                        </div>
                        <small class="text-muted">启用后，系统会根据下面的规则标识重点关注告警</small>
                    </div>

                    <hr>

                    <!-- 规则配置 -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label"><strong>关注规则</strong></label>
                            <button type="button" class="btn btn-primary btn-sm" onclick="addRule()">+ 添加规则</button>
                        </div>
                        <small class="text-muted mb-3 d-block">配置需要重点关注的告警关键词，系统会自动匹配告警名称中包含这些关键词的告警</small>

                        <div id="rules-container">
                            {% for rule in rules %}
                            <div class="rule-item border rounded p-3 mb-2">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">规则名称</label>
                                        <input type="text" class="form-control rule-name" value="{{ rule.name }}" placeholder="例如：设备掉电告警">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">关键词 <small class="text-muted">(用逗号分隔)</small></label>
                                        <input type="text" class="form-control rule-patterns" value="{{ rule.code_name_patterns|join(',') }}" placeholder="例如：设备掉电,电源断,停电">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger btn-sm" onclick="removeRule(this)">删除</button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 空状态提示 -->
                        <div id="empty-rules" class="text-center text-muted py-4" {% if rules %}style="display: none;"{% endif %}>
                            <p>暂无配置规则</p>
                            <button type="button" class="btn btn-outline-primary" onclick="addRule()">添加第一个规则</button>
                        </div>
                    </div>

                    <hr>

                    <!-- 操作按钮 -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">保存配置</button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                        <button type="button" class="btn btn-info" onclick="testRules()">测试规则</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 测试结果 -->
        <div id="test-results" class="card mt-3" style="display: none;">
            <div class="card-header">🧪 规则测试结果</div>
            <div class="card-body">
                <div id="test-content"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let ruleCounter = 0;

// 添加规则
function addRule() {
    const container = document.getElementById('rules-container');
    const emptyRules = document.getElementById('empty-rules');

    const ruleHtml = `
        <div class="rule-item border rounded p-3 mb-2">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">规则名称</label>
                    <input type="text" class="form-control rule-name" placeholder="例如：设备掉电告警">
                </div>
                <div class="col-md-6">
                    <label class="form-label">关键词 <small class="text-muted">(用逗号分隔)</small></label>
                    <input type="text" class="form-control rule-patterns" placeholder="例如：设备掉电,电源断,停电">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeRule(this)">删除</button>
                </div>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', ruleHtml);
    emptyRules.style.display = 'none';
}

// 删除规则
function removeRule(button) {
    const ruleItem = button.closest('.rule-item');
    ruleItem.remove();

    const container = document.getElementById('rules-container');
    const emptyRules = document.getElementById('empty-rules');

    if (container.children.length === 0) {
        emptyRules.style.display = 'block';
    }
}

// 收集表单数据
function collectFormData() {
    const enabled = document.getElementById('enabled').checked;
    const rules = [];

    document.querySelectorAll('.rule-item').forEach(item => {
        const name = item.querySelector('.rule-name').value.trim();
        const patterns = item.querySelector('.rule-patterns').value.trim();

        if (name && patterns) {
            rules.push({
                name: name,
                code_name_patterns: patterns.split(',').map(p => p.trim()).filter(p => p),
                priority: "medium"
            });
        }
    });

    return { enabled, rules };
}

// 保存配置
document.getElementById('focus-config-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const data = collectFormData();

    fetch('/api/focus-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('配置保存成功！');
            // 可以选择刷新页面或跳转
            // window.location.reload();
        } else {
            alert('保存失败：' + result.message);
        }
    })
    .catch(error => {
        alert('保存失败：' + error.message);
    });
});

// 重置表单
function resetForm() {
    if (confirm('确定要重置所有配置吗？')) {
        window.location.reload();
    }
}

// 测试规则
function testRules() {
    const data = collectFormData();

    if (!data.enabled || data.rules.length === 0) {
        alert('请先启用功能并添加至少一个规则');
        return;
    }

    // 模拟测试一些告警名称
    const testAlarms = [
        '设备掉电告警',
        '通信链路中断',
        '小区退出服务',
        '温度过高告警',
        '普通业务告警',
        '电源断开告警'
    ];

    const results = [];

    testAlarms.forEach(alarmName => {
        let matched = false;
        let matchedRule = null;

        for (const rule of data.rules) {
            for (const pattern of rule.code_name_patterns) {
                if (alarmName.includes(pattern)) {
                    matched = true;
                    matchedRule = rule.name;
                    break;
                }
            }
            if (matched) break;
        }

        results.push({
            alarm: alarmName,
            matched: matched,
            rule: matchedRule
        });
    });

    // 显示测试结果
    const testContent = document.getElementById('test-content');
    let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>告警名称</th><th>匹配状态</th><th>匹配规则</th></tr></thead><tbody>';

    results.forEach(result => {
        const statusClass = result.matched ? 'text-success' : 'text-muted';
        const statusText = result.matched ? '✅ 匹配' : '❌ 不匹配';
        const ruleText = result.rule || '-';

        html += `<tr><td>${result.alarm}</td><td class="${statusClass}">${statusText}</td><td>${ruleText}</td></tr>`;
    });

    html += '</tbody></table></div>';
    testContent.innerHTML = html;

    document.getElementById('test-results').style.display = 'block';
}
</script>
{% endblock %}