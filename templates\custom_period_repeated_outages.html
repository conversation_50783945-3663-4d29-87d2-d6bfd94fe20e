<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指定时间范围重复故障分析 - 菏泽移动网络故障分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .analysis-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .analysis-card:hover {
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .loading-spinner {
            display: none;
        }
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }
        .btn-export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-export:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            color: white;
        }
        .date-input-group {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-telephone-fill"></i>
                菏泽移动网络故障分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house-fill"></i> 首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="analysis-card card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-calendar-range"></i>
                            指定时间范围重复故障站点分析
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- 参数设置区域 -->
                        <div class="date-input-group">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="startDate" class="form-label">
                                        <i class="bi bi-calendar-event"></i> 起始日期
                                    </label>
                                    <input type="date" class="form-control" id="startDate" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="endDate" class="form-label">
                                        <i class="bi bi-calendar-check"></i> 结束日期
                                    </label>
                                    <input type="date" class="form-control" id="endDate" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="minOccurrences" class="form-label">
                                        <i class="bi bi-arrow-repeat"></i> 最小重复次数
                                    </label>
                                    <select class="form-select" id="minOccurrences">
                                        <option value="2" selected>2次</option>
                                        <option value="3">3次</option>
                                        <option value="4">4次</option>
                                        <option value="5">5次</option>
                                        <option value="10">10次</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-primary w-100" onclick="analyzeRepeatedOutages()">
                                        <i class="bi bi-search"></i> 开始分析
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div class="loading-spinner text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <p class="mt-2">正在分析重复故障数据，请稍候...</p>
                        </div>

                        <!-- 分析结果区域 -->
                        <div id="analysisResults" style="display: none;">
                            <!-- 统计汇总 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h3 id="totalDays">-</h3>
                                        <p class="mb-0">分析天数</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h3 id="totalRecords">-</h3>
                                        <p class="mb-0">总故障记录</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h3 id="repeatedSites">-</h3>
                                        <p class="mb-0">重复故障站点</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card text-center">
                                        <h3 id="totalOutages">-</h3>
                                        <p class="mb-0">总退服次数</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="bi bi-list-ul"></i> 重复故障站点详细列表
                                        </h5>
                                        <button type="button" class="btn btn-export" onclick="exportToExcel()">
                                            <i class="bi bi-file-earmark-excel"></i> 导出Excel
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据表格 -->
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="resultsTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>序号</th>
                                            <th>小区名称</th>
                                            <th>网格信息</th>
                                            <th>基站编码</th>
                                            <th>重复出现次数</th>
                                            <th>重复退服天数</th>
                                            <th>首次故障日期</th>
                                            <th>最后故障日期</th>
                                            <th>故障跨度天数</th>
                                            <th>总退服次数</th>
                                            <th>平均每次退服次数</th>
                                            <th>故障频率</th>
                                            <th>详细故障分布</th>
                                        </tr>
                                    </thead>
                                    <tbody id="resultsTableBody">
                                        <!-- 数据将通过JavaScript填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 无数据提示 -->
                        <div id="noDataMessage" style="display: none;" class="text-center py-5">
                            <i class="bi bi-info-circle text-muted" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mt-3">没有发现重复故障站点</h5>
                            <p class="text-muted">在指定时间范围内，没有找到符合条件的重复故障站点。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentAnalysisData = null;

        // 设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
        });

        function analyzeRepeatedOutages() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const minOccurrences = document.getElementById('minOccurrences').value;

            if (!startDate || !endDate) {
                alert('请选择起始日期和结束日期');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                alert('起始日期不能晚于结束日期');
                return;
            }

            // 显示加载状态
            document.querySelector('.loading-spinner').style.display = 'block';
            document.getElementById('analysisResults').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'none';

            // 发送分析请求
            fetch('/api/custom_period_repeated_outages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate,
                    min_occurrences: parseInt(minOccurrences)
                })
            })
            .then(response => response.json())
            .then(data => {
                document.querySelector('.loading-spinner').style.display = 'none';
                
                if (data.success) {
                    currentAnalysisData = data.data;
                    displayResults(data.data);
                } else {
                    alert('分析失败: ' + data.error);
                }
            })
            .catch(error => {
                document.querySelector('.loading-spinner').style.display = 'none';
                console.error('Error:', error);
                alert('分析请求失败，请检查网络连接');
            });
        }

        function displayResults(data) {
            if (data.repeated_sites_count === 0) {
                document.getElementById('noDataMessage').style.display = 'block';
                return;
            }

            // 更新统计数据
            document.getElementById('totalDays').textContent = data.total_days + '天';
            document.getElementById('totalRecords').textContent = data.total_records + '条';
            document.getElementById('repeatedSites').textContent = data.repeated_sites_count + '个';
            
            if (data.summary) {
                document.getElementById('totalOutages').textContent = data.summary.total_outages + '次';
            }

            // 填充表格数据
            const tableBody = document.getElementById('resultsTableBody');
            tableBody.innerHTML = '';

            data.repeated_outages.forEach((item, index) => {
                const row = tableBody.insertRow();
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td title="${item['小区名称']}">${item['小区名称'].length > 30 ? item['小区名称'].substring(0, 30) + '...' : item['小区名称']}</td>
                    <td>${item['网格信息']}</td>
                    <td>${item['基站编码']}</td>
                    <td><span class="badge bg-warning">${item['重复出现次数']}</span></td>
                    <td><span class="badge bg-danger">${item['重复退服天数']}</span></td>
                    <td>${item['首次故障日期']}</td>
                    <td>${item['最后故障日期']}</td>
                    <td>${item['故障跨度天数']}</td>
                    <td><span class="badge bg-info">${item['总退服次数']}</span></td>
                    <td>${item['平均每次退服次数']}</td>
                    <td>${item['故障频率']}</td>
                    <td title="${item['详细故障分布']}">${item['详细故障分布'].length > 50 ? item['详细故障分布'].substring(0, 50) + '...' : item['详细故障分布']}</td>
                `;
            });

            document.getElementById('analysisResults').style.display = 'block';
        }

        function exportToExcel() {
            if (!currentAnalysisData) {
                alert('请先进行分析');
                return;
            }

            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const minOccurrences = document.getElementById('minOccurrences').value;

            // 显示导出进度
            const exportBtn = document.querySelector('.btn-export');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
            exportBtn.disabled = true;

            // 发送导出请求
            fetch('/api/custom_period_repeated_outages/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate,
                    min_occurrences: parseInt(minOccurrences)
                })
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || '导出失败');
                    });
                }
            })
            .then(blob => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `指定时间范围重复故障分析-${startDate}至${endDate}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 显示成功提示
                alert('Excel文件导出成功！');
            })
            .catch(error => {
                console.error('导出错误:', error);
                alert('导出失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
            });
        }
    </script>
</body>
</html>
