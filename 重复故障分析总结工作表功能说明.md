# 重复故障分析总结工作表功能说明

## 🎯 **功能概述**

为重复故障分析Excel导出添加了**区县总结工作表**，提供按区县统计的重复故障概览，让用户能够快速了解各区县的故障分布情况。

## 📊 **总结工作表设计**

### **工作表结构**
```
📋 Excel文件包含2个工作表:
├── 区县总结 (第1个工作表) ← 新增功能
└── 重复故障分析 (第2个工作表) ← 原有详细数据
```

### **区县总结工作表内容**
```
第1行: 标题 - "重复故障分析区县总结 - 2025-06-25"
第2行: 分析信息 - 时间范围、模式、参数说明
第3行: (空行)
第4行: 表头 - 10列统计指标
第5行+: 数据 - 按重复故障小区数排序的区县数据
```

### **统计指标说明**
```
📊 10列统计指标:
├── 区县: 区县名称
├── 重复故障小区数: 该区县的重复故障小区总数
├── 平均重复次数: 该区县重复故障的平均次数
├── 最高重复次数: 该区县单个小区的最高重复次数
├── 累计退服次数: 该区县所有重复故障的总退服次数
├── 严重故障(≥10次): 重复次数≥10次的小区数量
├── 警告故障(≥5次): 重复次数≥5次的小区数量
├── 关注故障(≥3次): 重复次数≥3次的小区数量
├── 故障率: 该区县重复故障小区占总数的百分比
└── 排名: 按重复故障小区数的排名
```

## 🎨 **视觉设计特性**

### **颜色编码系统**
```
🎨 智能颜色编码:
├── 有严重故障的区县: 红色背景 (#FFE6E6)
├── 有警告故障的区县: 黄色背景 (#FFF2CC)
├── 有关注故障的区县: 蓝色背景 (#E6F3FF)
└── 一般故障区县: 无背景色

颜色优先级: 严重 > 警告 > 关注 > 一般
```

### **格式设置**
```
🔤 字体和格式:
├── 标题: 微软雅黑 14pt 粗体
├── 表头: 微软雅黑 12pt 粗体 蓝色背景 白色字体
├── 数据: 微软雅黑 10pt 常规 居中对齐
└── 边框: 全表格细边框

📐 列宽设置:
├── 区县: 12 | 重复故障小区数: 15 | 平均重复次数: 12
├── 最高重复次数: 12 | 累计退服次数: 12
├── 严重故障: 15 | 警告故障: 15 | 关注故障: 15
├── 故障率: 10 | 排名: 8
└── 行高: 标题25px, 表头30px, 数据25px
```

## 📈 **数据统计逻辑**

### **统计算法**
```python
# 区县数据统计
for outage in repeated_outages:
    grid = outage['grid']
    stats = grid_stats[grid]
    
    # 基础统计
    stats['count'] += 1  # 重复故障小区数
    stats['total_repeat_count'] += outage['repeat_count']  # 总重复次数
    stats['max_repeat_count'] = max(stats['max_repeat_count'], outage['repeat_count'])  # 最高重复次数
    stats['total_outages'] += outage['total_outages']  # 累计退服次数
    
    # 严重程度分类
    if outage['repeat_count'] >= 10:
        stats['critical_count'] += 1  # 严重故障
    elif outage['repeat_count'] >= 5:
        stats['warning_count'] += 1   # 警告故障
    elif outage['repeat_count'] >= 3:
        stats['attention_count'] += 1 # 关注故障

# 计算衍生指标
avg_repeat = stats['total_repeat_count'] / stats['count']  # 平均重复次数
fault_rate = (stats['count'] / total_repeated) * 100      # 故障率
```

### **排序规则**
```
📊 区县排序规则:
1. 按重复故障小区数降序排列
2. 故障小区数多的区县排在前面
3. 自动生成排名（1, 2, 3...）
```

## 🚀 **技术实现**

### **代码结构**
```python
def generate_repeated_outages_excel_attachment(analysis_result):
    # 1. 创建Excel工作簿
    wb = Workbook()
    ws = wb.active  # 详细数据工作表
    
    # 2. 生成区县统计数据
    grid_stats = {}
    for outage in repeated_outages:
        # 统计各区县的故障情况
    
    # 3. 创建总结工作表（插入到第一位）
    summary_ws = wb.create_sheet("区县总结", 0)
    
    # 4. 填充总结工作表内容
    # - 标题和信息
    # - 表头设置
    # - 数据填充和颜色编码
    # - 列宽和行高调整
    
    # 5. 保存Excel文件
    return temp_path
```

### **工作表管理**
```python
# 工作表创建顺序
summary_ws = wb.create_sheet("区县总结", 0)  # 插入到第一个位置
ws.title = "重复故障分析"                    # 原有工作表重命名

# 最终工作表顺序
1. 区县总结 (总览)
2. 重复故障分析 (详细)
```

## 📋 **实际应用示例**

### **总结数据示例**
```
区县总结工作表内容:
┌─────────┬────────────┬──────────┬──────────┬──────────┐
│ 区县    │重复故障小区数│平均重复次数│最高重复次数│累计退服次数│
├─────────┼────────────┼──────────┼──────────┼──────────┤
│ 牡丹区  │     15     │   8.2次  │   17次   │    156   │
│ 东明县  │     12     │   6.5次  │   14次   │    98    │
│ 巨野县  │      8     │   5.1次  │   11次   │    67    │
│ 成武县  │      6     │   4.3次  │    8次   │    34    │
│ 单县    │      4     │   3.8次  │    6次   │    23    │
└─────────┴────────────┴──────────┴──────────┴──────────┘

继续显示: 严重故障、警告故障、关注故障、故障率、排名
```

### **颜色编码效果**
```
🔴 牡丹区: 红色背景 (有严重故障≥10次)
🟡 东明县: 黄色背景 (有警告故障≥5次)
🔵 巨野县: 蓝色背景 (有关注故障≥3次)
⚪ 成武县: 无背景色 (一般故障)
⚪ 单县: 无背景色 (一般故障)
```

## 🎯 **用户价值**

### **管理决策支持**
```
✅ 快速识别问题区县: 一目了然的排名和颜色编码
✅ 资源分配依据: 根据故障严重程度分配维护资源
✅ 绩效评估工具: 各区县故障率对比分析
✅ 趋势监控基础: 为后续趋势分析提供数据基础
```

### **工作效率提升**
```
✅ 总览在前: 区县总结作为第一个工作表，优先查看
✅ 详细在后: 需要时可查看具体故障小区详情
✅ 数据完整: 一个Excel文件包含总结和详细两个维度
✅ 格式专业: 企业级报告格式，可直接用于汇报
```

### **分析能力增强**
```
✅ 多维度统计: 数量、频率、严重程度等多个维度
✅ 智能排序: 按故障严重程度自动排序
✅ 视觉突出: 颜色编码快速识别重点区县
✅ 数据关联: 总结和详细数据相互印证
```

## 🔧 **使用方法**

### **重复故障分析页面导出**
1. 进入重复故障分析页面
2. 设置分析参数（日期、模式、重复次数）
3. 点击"导出Excel"按钮
4. 下载包含总结工作表的Excel文件

### **邮件发送附件**
1. 进入邮件发送页面
2. 勾选"自动生成重复故障分析附件"
3. 配置重复故障分析参数
4. 发送邮件，附件包含总结工作表

### **Excel文件查看**
1. 打开下载的Excel文件
2. 首先查看"区县总结"工作表了解整体情况
3. 需要时切换到"重复故障分析"工作表查看详细信息

## 🔮 **后续优化方向**

### **功能增强**
- 添加区县故障趋势图表
- 支持自定义统计维度
- 增加同比环比分析

### **交互改进**
- 支持总结和详细数据的超链接跳转
- 添加数据透视表功能
- 集成更多可视化图表

---

**功能状态**: ✅ 完成并集成  
**技术特色**: 双工作表 + 智能统计 + 颜色编码  
**应用价值**: 管理决策 + 效率提升 + 分析增强  
**用户体验**: 总览优先 + 详细备查 + 专业格式  

重复故障分析总结工作表功能为用户提供了更加全面和实用的数据分析工具！🎉
