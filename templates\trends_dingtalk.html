<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势图钉钉推送 - 菏泽移网数据处理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .loading {
            display: none;
        }
        .preview-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-graph-up-arrow"></i>
                菏泽移网数据处理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="/multi_district_trends">
                    <i class="bi bi-bar-chart-line"></i> 趋势分析
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="display-6 text-primary">
                        <i class="bi bi-send"></i>
                        趋势图钉钉推送
                    </h1>
                    <p class="lead text-muted">生成合并趋势图并发送到钉钉群</p>
                </div>
            </div>
        </div>

        <!-- 功能介绍 -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="text-center">
                    <i class="bi bi-graph-up feature-icon"></i>
                    <h5>合并趋势图</h5>
                    <p class="text-muted">将所有区县的趋势线合并到一张大图中</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="bi bi-eye feature-icon"></i>
                    <h5>直观对比</h5>
                    <p class="text-muted">便于直观比较各区县的故障趋势变化</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="bi bi-chat-dots feature-icon"></i>
                    <h5>钉钉推送</h5>
                    <p class="text-muted">一键发送到钉钉群，方便团队查看</p>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i>
                            趋势图配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="trendsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="startDate" class="form-label">
                                            <i class="bi bi-calendar-event"></i>
                                            开始日期
                                        </label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="endDate" class="form-label">
                                            <i class="bi bi-calendar-check"></i>
                                            结束日期
                                        </label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="dingtalkApi" class="form-label">
                                    <i class="bi bi-chat-dots"></i>
                                    钉钉API配置
                                </label>
                                <select class="form-select" id="dingtalkApi" required>
                                    <option value="">正在加载钉钉API配置...</option>
                                </select>
                                <small class="form-text text-muted">选择要使用的钉钉群配置</small>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeTargetLine" checked>
                                    <label class="form-check-label" for="includeTargetLine">
                                        <i class="bi bi-bullseye"></i>
                                        包含目标线
                                    </label>
                                    <small class="form-text text-muted d-block">显示月度目标进度线，便于对比实际值与目标值</small>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send"></i>
                                    生成并发送趋势图
                                </button>
                            </div>
                        </form>

                        <!-- 加载指示器 -->
                        <div class="loading text-center mt-3" id="loadingIndicator">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">生成中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在生成趋势图并上传到钉钉...</p>
                        </div>

                        <!-- 结果显示 -->
                        <div id="resultContainer" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i>
                            使用说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-1-circle text-primary"></i> 选择日期范围</h6>
                                <p class="text-muted">选择要分析的开始和结束日期，建议选择7-30天的范围以获得最佳显示效果。</p>
                                
                                <h6><i class="bi bi-2-circle text-primary"></i> 配置选项</h6>
                                <p class="text-muted">选择是否包含目标线，目标线可以帮助对比实际故障数与预期目标。</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-3-circle text-primary"></i> 生成图表</h6>
                                <p class="text-muted">系统将自动从数据库获取数据，生成包含所有区县的合并趋势图。</p>
                                
                                <h6><i class="bi bi-4-circle text-primary"></i> 钉钉推送</h6>
                                <p class="text-muted">图表生成后将自动上传到图床并发送到配置的钉钉群。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 设置默认日期（最近7天）和加载钉钉API配置
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 6);

            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];

            // 加载钉钉API配置
            loadDingtalkApis();
        });

        // 加载钉钉API配置
        function loadDingtalkApis() {
            fetch('/api/get_dingtalk_apis')
                .then(response => response.json())
                .then(data => {
                    const select = document.getElementById('dingtalkApi');
                    select.innerHTML = '';

                    if (data.apis && Object.keys(data.apis).length > 0) {
                        // 添加默认选项
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.textContent = '请选择钉钉API配置';
                        select.appendChild(defaultOption);

                        // 添加API选项
                        for (const [apiId, apiInfo] of Object.entries(data.apis)) {
                            if (apiInfo.enabled) {
                                const option = document.createElement('option');
                                option.value = apiId;
                                option.textContent = apiInfo.name;
                                if (apiInfo.is_current) {
                                    option.selected = true;
                                }
                                select.appendChild(option);
                            }
                        }

                        // 如果有当前选中的API，自动选择
                        if (data.current) {
                            select.value = data.current;
                        }
                    } else {
                        const option = document.createElement('option');
                        option.value = '';
                        option.textContent = '未找到可用的钉钉API配置';
                        select.appendChild(option);
                    }
                })
                .catch(error => {
                    console.error('加载钉钉API配置失败:', error);
                    const select = document.getElementById('dingtalkApi');
                    select.innerHTML = '<option value="">加载钉钉API配置失败</option>';
                });
        }

        // 表单提交处理
        document.getElementById('trendsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const includeTargetLine = document.getElementById('includeTargetLine').checked;
            const dingtalkApi = document.getElementById('dingtalkApi').value;

            if (!startDate || !endDate) {
                showAlert('请选择开始和结束日期', 'warning');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showAlert('开始日期不能晚于结束日期', 'warning');
                return;
            }

            if (!dingtalkApi) {
                showAlert('请选择钉钉API配置', 'warning');
                return;
            }

            sendTrendsToDigtalk(startDate, endDate, includeTargetLine, dingtalkApi);
        });

        // 发送趋势图到钉钉
        function sendTrendsToDigtalk(startDate, endDate, includeTargetLine, dingtalkApi) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const resultContainer = document.getElementById('resultContainer');

            // 显示加载指示器
            loadingIndicator.style.display = 'block';
            resultContainer.innerHTML = '';

            // 先切换钉钉API
            fetch('/api/set_dingtalk_api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    api_id: dingtalkApi
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // API切换成功，发送趋势图
                    return fetch('/api/dingtalk_send_trends', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            start_date: startDate,
                            end_date: endDate,
                            include_target_line: includeTargetLine
                        })
                    });
                } else {
                    throw new Error('切换钉钉API失败: ' + data.error);
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingIndicator.style.display = 'none';

                if (data.success) {
                    showResult('success', '趋势图发送成功！', data.details || data.message, data.image_url);
                } else {
                    showResult('danger', '发送失败', data.error);
                }
            })
            .catch(error => {
                loadingIndicator.style.display = 'none';
                showResult('danger', '请求失败', error.message);
            });
        }

        // 显示结果
        function showResult(type, title, message, imageUrl = null) {
            const resultContainer = document.getElementById('resultContainer');
            
            let imagePreview = '';
            if (imageUrl) {
                imagePreview = `
                    <div class="preview-container mt-3">
                        <h6><i class="bi bi-image"></i> 图片预览</h6>
                        <img src="${imageUrl}" class="img-fluid rounded" alt="趋势图预览" style="max-height: 400px;">
                        <p class="text-muted mt-2">
                            <i class="bi bi-link-45deg"></i>
                            图片链接: <a href="${imageUrl}" target="_blank">${imageUrl}</a>
                        </p>
                    </div>
                `;
            }
            
            resultContainer.innerHTML = `
                <div class="alert alert-${type}" role="alert">
                    <h6 class="alert-heading">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                        ${title}
                    </h6>
                    <p class="mb-0">${message}</p>
                    ${imagePreview}
                </div>
            `;
        }

        // 显示提示信息
        function showAlert(message, type) {
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.innerHTML = `
                <div class="alert alert-${type}" role="alert">
                    <i class="bi bi-info-circle"></i>
                    ${message}
                </div>
            `;
        }
    </script>
</body>
</html>
