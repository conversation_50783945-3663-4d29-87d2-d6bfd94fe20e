/* 本地XLSX功能 - 离线版本 */

// 简化的XLSX库，用于离线环境
(function() {
    'use strict';

    // 简化的工作簿对象
    function Workbook() {
        this.SheetNames = [];
        this.Sheets = {};
    }

    // 简化的工作表对象
    function Worksheet() {
        this['!ref'] = '';
        this['!cols'] = [];
        this['!rows'] = [];
    }

    // 将数组转换为工作表
    function aoa_to_sheet(data, opts) {
        var ws = new Worksheet();
        var range = {s: {c: 0, r: 0}, e: {c: 0, r: 0}};
        
        if (data.length === 0) return ws;
        
        // 计算范围
        range.e.r = data.length - 1;
        range.e.c = 0;
        
        for (var R = 0; R < data.length; R++) {
            if (Array.isArray(data[R])) {
                if (data[R].length > range.e.c) {
                    range.e.c = data[R].length - 1;
                }
                for (var C = 0; C < data[R].length; C++) {
                    var cell_ref = encode_cell({c: C, r: R});
                    var cell_value = data[R][C];
                    if (cell_value === null || cell_value === undefined) continue;
                    
                    var cell = {v: cell_value};
                    if (typeof cell_value === 'number') {
                        cell.t = 'n';
                    } else if (typeof cell_value === 'boolean') {
                        cell.t = 'b';
                    } else {
                        cell.t = 's';
                        cell.v = String(cell_value);
                    }
                    ws[cell_ref] = cell;
                }
            }
        }
        
        ws['!ref'] = encode_range(range);
        return ws;
    }

    // 编码单元格引用
    function encode_cell(cell) {
        return encode_col(cell.c) + encode_row(cell.r);
    }

    // 编码列
    function encode_col(col) {
        var s = '';
        for (++col; col; col = Math.floor((col - 1) / 26)) {
            s = String.fromCharCode(((col - 1) % 26) + 65) + s;
        }
        return s;
    }

    // 编码行
    function encode_row(row) {
        return String(row + 1);
    }

    // 编码范围
    function encode_range(range) {
        return encode_cell(range.s) + ':' + encode_cell(range.e);
    }

    // 创建新工作簿
    function book_new() {
        return new Workbook();
    }

    // 向工作簿添加工作表
    function book_append_sheet(wb, ws, name) {
        if (!name) name = 'Sheet' + (wb.SheetNames.length + 1);
        wb.SheetNames.push(name);
        wb.Sheets[name] = ws;
    }

    // 将工作簿转换为CSV格式
    function sheet_to_csv(ws, opts) {
        var out = '';
        var r = decode_range(ws['!ref'] || 'A1:A1');
        var row, col;
        
        for (row = r.s.r; row <= r.e.r; ++row) {
            var rowData = [];
            for (col = r.s.c; col <= r.e.c; ++col) {
                var cell_ref = encode_cell({c: col, r: row});
                var cell = ws[cell_ref];
                var val = '';
                if (cell) {
                    val = cell.v || '';
                    if (typeof val === 'string' && val.indexOf(',') !== -1) {
                        val = '"' + val.replace(/"/g, '""') + '"';
                    }
                }
                rowData.push(val);
            }
            out += rowData.join(',') + '\n';
        }
        return out;
    }

    // 解码范围
    function decode_range(range) {
        var parts = range.split(':');
        return {
            s: decode_cell(parts[0]),
            e: decode_cell(parts[1] || parts[0])
        };
    }

    // 解码单元格
    function decode_cell(cell) {
        var match = cell.match(/^([A-Z]+)(\d+)$/);
        if (!match) return {c: 0, r: 0};
        return {
            c: decode_col(match[1]),
            r: parseInt(match[2]) - 1
        };
    }

    // 解码列
    function decode_col(col) {
        var result = 0;
        for (var i = 0; i < col.length; i++) {
            result = result * 26 + (col.charCodeAt(i) - 64);
        }
        return result - 1;
    }

    // 写入文件（离线版本）
    function writeFile(wb, filename, opts) {
        try {
            // 尝试使用现代浏览器的下载功能
            var csv_content = '';
            
            // 转换第一个工作表为CSV
            if (wb.SheetNames.length > 0) {
                var ws = wb.Sheets[wb.SheetNames[0]];
                csv_content = sheet_to_csv(ws);
            }
            
            // 创建Blob并下载
            var blob = new Blob([csv_content], {type: 'text/csv;charset=utf-8;'});
            var link = document.createElement('a');
            
            if (link.download !== undefined) {
                var url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename.replace(/\.xlsx?$/, '.csv'));
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            } else {
                // 降级方案：显示内容让用户手动复制
                var popup = window.open('', '_blank');
                popup.document.write('<pre>' + csv_content + '</pre>');
                popup.document.title = filename;
                alert('离线模式下已在新窗口中显示数据，请手动复制保存为CSV文件。');
            }
        } catch (e) {
            // 最终降级方案
            console.error('文件下载失败:', e);
            alert('离线模式下无法自动下载文件。请确保网络连接正常，或手动复制数据。');
        }
    }

    // 导出XLSX对象
    window.XLSX = {
        version: '0.18.5-local',
        utils: {
            book_new: book_new,
            aoa_to_sheet: aoa_to_sheet,
            book_append_sheet: book_append_sheet,
            sheet_to_csv: sheet_to_csv,
            encode_cell: encode_cell,
            encode_range: encode_range,
            decode_range: decode_range
        },
        writeFile: writeFile,
        
        // 兼容性方法
        read: function(data, opts) {
            console.warn('XLSX.read 在离线模式下不可用');
            return book_new();
        },
        
        write: function(wb, opts) {
            console.warn('XLSX.write 在离线模式下使用简化版本');
            if (wb.SheetNames.length > 0) {
                return sheet_to_csv(wb.Sheets[wb.SheetNames[0]]);
            }
            return '';
        }
    };

    // 添加到全局作用域
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = window.XLSX;
    }

})();
