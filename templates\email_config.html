<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件配置 - 菏泽数据处理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2E5BBA, #4472C4);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #2E5BBA;
            box-shadow: 0 0 0 3px rgba(46, 91, 186, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-group .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .email-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .email-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .email-tag .remove {
            cursor: pointer;
            color: #f44336;
            font-weight: bold;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2E5BBA, #4472C4);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 91, 186, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #2E5BBA;
        }

        .section h3 {
            color: #2E5BBA;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .current-config {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .current-config h4 {
            color: #2E5BBA;
            margin-bottom: 10px;
        }

        .current-config .config-item {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .config-item strong {
            color: #333;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .content {
                padding: 20px;
            }

            .btn-group {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 邮件配置管理</h1>
            <p>配置邮件收件人、抄送和密送列表</p>
        </div>

        <div class="content">
            <div id="alert" class="alert"></div>

            <!-- 当前配置显示 -->
            <div class="current-config">
                <h4>📋 当前邮件配置</h4>
                <div id="current-config-content">
                    <div class="config-item"><strong>发件人：</strong><span id="current-sender">加载中...</span></div>
                    <div class="config-item"><strong>默认收件人：</strong><span id="current-recipients">加载中...</span></div>
                    <div class="config-item"><strong>默认抄送：</strong><span id="current-cc">加载中...</span></div>
                    <div class="config-item"><strong>默认密送：</strong><span id="current-bcc">加载中...</span></div>
                </div>
            </div>

            <form id="emailConfigForm">
                <!-- 发件人配置 -->
                <div class="section">
                    <h3>👤 发件人配置</h3>
                    <div class="form-group">
                        <label for="emailAccount">邮箱账户</label>
                        <select id="emailAccount" name="emailAccount">
                            <option value="">加载中...</option>
                        </select>
                        <div class="help-text">选择用于发送邮件的邮箱账户</div>
                    </div>
                    <div class="form-group">
                        <label for="senderName">发件人姓名</label>
                        <input type="text" id="senderName" name="senderName" placeholder="例如：徐嘉昕">
                        <div class="help-text">显示在邮件发件人字段的姓名</div>
                    </div>
                </div>

                <!-- 收件人配置 -->
                <div class="section">
                    <h3>📨 默认收件人配置</h3>
                    <div class="form-group">
                        <label for="recipients">收件人邮箱</label>
                        <textarea id="recipients" name="recipients" placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="help-text">每行输入一个邮箱地址，这些是默认的主要收件人</div>
                        <div id="recipients-list" class="email-list"></div>
                    </div>
                </div>

                <!-- 抄送配置 -->
                <div class="section">
                    <h3>📋 默认抄送配置</h3>
                    <div class="form-group">
                        <label for="cc">抄送邮箱</label>
                        <textarea id="cc" name="cc" placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="help-text">每行输入一个邮箱地址，这些人会收到邮件副本</div>
                        <div id="cc-list" class="email-list"></div>
                    </div>
                </div>

                <!-- 密送配置 -->
                <div class="section">
                    <h3>🔒 默认密送配置</h3>
                    <div class="form-group">
                        <label for="bcc">密送邮箱</label>
                        <textarea id="bcc" name="bcc" placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="help-text">每行输入一个邮箱地址，这些人会收到邮件但其他收件人看不到</div>
                        <div id="bcc-list" class="email-list"></div>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">💾 保存配置</button>
                    <button type="button" class="btn btn-secondary" onclick="loadCurrentConfig()">🔄 重新加载</button>
                    <button type="button" class="btn btn-success" onclick="smartReturn()">
                        <i class="bi bi-arrow-left"></i> <span id="returnText">返回</span>
                    </button>
                    <a href="/" class="btn btn-outline-secondary">🏠 首页</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 页面加载时获取当前配置和设置返回按钮
        document.addEventListener('DOMContentLoaded', function() {
            loadEmailAccounts();
            loadCurrentConfig();
            setupSmartReturn();
        });

        // 加载邮箱账户列表
        function loadEmailAccounts() {
            fetch('/api/get_email_accounts')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('emailAccount');
                        select.innerHTML = '';

                        data.accounts.forEach(account => {
                            const option = document.createElement('option');
                            option.value = account.account_id;
                            option.textContent = `${account.name} (${account.email})`;
                            if (account.is_default) {
                                option.selected = true;
                            }
                            select.appendChild(option);
                        });

                        // 加载当前选中的邮箱账户
                        loadCurrentEmailAccount();
                    } else {
                        showAlert('error', '加载邮箱账户失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', '加载邮箱账户失败: ' + error.message);
                });
        }

        // 加载当前邮箱账户
        function loadCurrentEmailAccount() {
            fetch('/api/get_current_email_account')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.account) {
                        const select = document.getElementById('emailAccount');
                        select.value = data.account.account_id;

                        // 更新当前配置显示中的发件人信息
                        document.getElementById('current-sender').textContent =
                            `${data.account.name} (${data.account.email})`;
                    }
                })
                .catch(error => {
                    console.error('加载当前邮箱账户失败:', error);
                });
        }

        // 邮箱账户选择变化时的处理
        document.getElementById('emailAccount').addEventListener('change', function() {
            const accountId = this.value;
            if (accountId) {
                fetch('/api/set_current_email_account', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ account_id: accountId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', data.message);
                        loadCurrentEmailAccount(); // 重新加载当前账户信息
                    } else {
                        showAlert('error', '切换邮箱账户失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', '切换邮箱账户失败: ' + error.message);
                });
            }
        });

        // 加载当前配置
        function loadCurrentConfig() {
            fetch('/api/email_config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        
                        // 更新当前配置显示
                        document.getElementById('current-sender').textContent = 
                            `${config.sender_name} <${config.sender_email}>`;
                        document.getElementById('current-recipients').textContent = 
                            config.default_recipients.length > 0 ? config.default_recipients.join(', ') : '无';
                        document.getElementById('current-cc').textContent = 
                            config.default_cc.length > 0 ? config.default_cc.join(', ') : '无';
                        document.getElementById('current-bcc').textContent = 
                            config.default_bcc.length > 0 ? config.default_bcc.join(', ') : '无';
                        
                        // 填充表单
                        document.getElementById('senderName').value = config.sender_name || '';
                        document.getElementById('recipients').value = config.default_recipients.join('\n');
                        document.getElementById('cc').value = config.default_cc.join('\n');
                        document.getElementById('bcc').value = config.default_bcc.join('\n');
                        
                        // 更新邮箱标签显示
                        updateEmailTags('recipients', config.default_recipients);
                        updateEmailTags('cc', config.default_cc);
                        updateEmailTags('bcc', config.default_bcc);
                    } else {
                        showAlert('error', '加载配置失败: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', '加载配置失败: ' + error.message);
                });
        }

        // 更新邮箱标签显示
        function updateEmailTags(fieldName, emails) {
            const container = document.getElementById(fieldName + '-list');
            container.innerHTML = '';
            
            emails.forEach(email => {
                if (email.trim()) {
                    const tag = document.createElement('div');
                    tag.className = 'email-tag';
                    tag.innerHTML = `
                        ${email.trim()}
                        <span class="remove" onclick="removeEmail('${fieldName}', '${email.trim()}')">&times;</span>
                    `;
                    container.appendChild(tag);
                }
            });
        }

        // 删除邮箱
        function removeEmail(fieldName, email) {
            const textarea = document.getElementById(fieldName);
            const emails = textarea.value.split('\n').filter(e => e.trim() !== email);
            textarea.value = emails.join('\n');
            updateEmailTags(fieldName, emails.filter(e => e.trim()));
        }

        // 监听文本框变化，实时更新标签
        ['recipients', 'cc', 'bcc'].forEach(fieldName => {
            document.getElementById(fieldName).addEventListener('input', function() {
                const emails = this.value.split('\n').filter(e => e.trim());
                updateEmailTags(fieldName, emails);
            });
        });

        // 表单提交
        document.getElementById('emailConfigForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                sender_name: document.getElementById('senderName').value.trim(),
                default_recipients: document.getElementById('recipients').value
                    .split('\n').map(e => e.trim()).filter(e => e),
                default_cc: document.getElementById('cc').value
                    .split('\n').map(e => e.trim()).filter(e => e),
                default_bcc: document.getElementById('bcc').value
                    .split('\n').map(e => e.trim()).filter(e => e)
            };
            
            fetch('/api/email_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', '邮件配置保存成功！');
                    loadCurrentConfig(); // 重新加载配置
                } else {
                    showAlert('error', '保存失败: ' + data.error);
                }
            })
            .catch(error => {
                showAlert('error', '保存失败: ' + error.message);
            });
        });

        // 显示提示信息
        function showAlert(type, message) {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type === 'success' ? 'success' : 'error'}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // 智能返回功能
        function setupSmartReturn() {
            const urlParams = new URLSearchParams(window.location.search);
            const returnUrl = urlParams.get('return_url');
            const fromResults = urlParams.get('from_results');

            const returnTextElement = document.getElementById('returnText');

            if (returnUrl) {
                // 如果有明确的返回URL
                returnTextElement.textContent = '返回上页';
            } else if (fromResults === 'true') {
                // 如果来自结果页面
                returnTextElement.textContent = '返回结果页';
            } else if (document.referrer && !document.referrer.includes('/email_config')) {
                // 如果有来源页面且不是邮件配置页面本身
                returnTextElement.textContent = '返回上页';
            } else {
                // 默认返回首页
                returnTextElement.textContent = '返回首页';
            }
        }

        function smartReturn() {
            const urlParams = new URLSearchParams(window.location.search);
            const returnUrl = urlParams.get('return_url');
            const fromResults = urlParams.get('from_results');

            console.log('邮件配置智能返回调试信息:');
            console.log('当前URL:', window.location.href);
            console.log('returnUrl参数:', returnUrl);
            console.log('fromResults参数:', fromResults);
            console.log('document.referrer:', document.referrer);

            if (returnUrl) {
                // 如果有明确的返回URL，解码并跳转
                const decodedUrl = decodeURIComponent(returnUrl);
                console.log('使用returnUrl返回:', decodedUrl);

                // 如果返回URL是upload，重定向到首页
                if (decodedUrl.includes('/upload')) {
                    console.log('upload URL重定向到首页');
                    window.location.href = '/';
                } else {
                    window.location.href = decodedUrl;
                }
            } else if (fromResults === 'true') {
                // 如果来自结果页面，尝试返回结果页面
                const date = urlParams.get('date');
                if (date) {
                    console.log('返回结果页面，日期:', date);
                    window.location.href = `/results?date=${date}&from_history=true`;
                } else {
                    console.log('返回历史记录页面');
                    window.location.href = '/history';
                }
            } else if (document.referrer && !document.referrer.includes('/email_config') && !document.referrer.includes('127.0.0.1:5000/email_config')) {
                // 如果有来源页面且不是邮件配置页面本身，返回来源页面
                console.log('使用history.back()返回');
                window.history.back();
            } else {
                // 默认返回首页
                console.log('默认返回首页');
                window.location.href = '/';
            }
        }
    </script>
</body>
</html>
