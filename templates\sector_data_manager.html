{% extends "base.html" %}

{% block title %}扇区数据管理{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-database"></i> 扇区数据管理</h2>
                <div>
                    <a href="/" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-house"></i> 返回首页
                    </a>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="bi bi-upload"></i> 上传数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ summary.total_records if summary else 0 }}</h4>
                            <p class="card-text">总记录数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-database-fill" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">
                                {% if summary and summary.date_range[0] and summary.date_range[1] %}
                                    {{ (summary.date_range[1] | string)[:10] }}
                                {% else %}
                                    无数据
                                {% endif %}
                            </h4>
                            <p class="card-text">最新数据日期</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-calendar-date" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">
                                {% if summary and summary.date_range[0] and summary.date_range[1] %}
                                    {{ ((summary.date_range[1] | string)[:10] | string).replace(((summary.date_range[0] | string)[:7] | string), '').replace('-', '') }}天
                                {% else %}
                                    0天
                                {% endif %}
                            </h4>
                            <p class="card-text">数据覆盖天数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ summary.recent_imports|length if summary else 0 }}</h4>
                            <p class="card-text">最近导入次数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-arrow-up-circle" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-tools"></i> 数据操作</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新统计
                        </button>
                        <div class="btn-group" role="group">
                            <a href="/export_sector_data" class="btn btn-outline-success">
                                <i class="bi bi-download"></i> 导出全部
                            </a>
                            <button type="button" class="btn btn-outline-success" onclick="showExportModal()">
                                <i class="bi bi-funnel"></i> 筛选导出
                            </button>
                        </div>
                        <button type="button" class="btn btn-outline-danger" onclick="clearDatabase()">
                            <i class="bi bi-trash"></i> 清空数据库
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近导入记录 -->
    {% if summary and summary.recent_imports %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-clock-history"></i> 最近导入记录</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>日期范围</th>
                                    <th>总记录数</th>
                                    <th>成功导入</th>
                                    <th>导入时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for import_record in summary.recent_imports %}
                                <tr>
                                    <td>{{ import_record[0] }}</td>
                                    <td>{{ import_record[1] }}</td>
                                    <td>{{ import_record[2] }}</td>
                                    <td><span class="badge bg-success">{{ import_record[3] }}</span></td>
                                    <td>{{ import_record[4] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 每日统计 -->
    {% if summary and summary.daily_stats %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-bar-chart"></i> 每日数据统计</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>记录数量</th>
                                    <th>总退服次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for daily_stat in summary.daily_stats %}
                                <tr>
                                    <td>{{ daily_stat[0] }}</td>
                                    <td><span class="badge bg-info">{{ daily_stat[1] }}</span></td>
                                    <td><span class="badge bg-warning">{{ daily_stat[2] }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 上传文件模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传扇区数据文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="fileInput" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">
                            <strong>支持的文件格式：</strong>.xlsx, .xls<br>
                            <strong>文件应包含：</strong>序号、账期、地市、区县、网格、小区名称、基站编码、总退服次数等列<br>
                            <strong>数据存储说明：</strong>区县字段不会保存到数据库（数据不准确）<br>
                            <strong>重复检测规则：</strong><br>
                            • 文件内的重复记录会被保留（如同一小区多次故障）<br>
                            • 只跳过与数据库中已有记录的重复<br>
                            • 基于"日期+小区名称+退服次数"判断重复
                        </div>
                    </div>
                    <div id="uploadProgress" class="progress mb-3" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="uploadResult" class="alert" style="display: none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="bi bi-upload"></i> 上传
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div id="exportModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div style="background-color: white; margin: 10% auto; padding: 20px; border-radius: 8px; width: 500px; max-width: 90%;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h5 style="margin: 0;">筛选导出扇区数据</h5>
            <button type="button" onclick="closeExportModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">时间范围：</label>
            <div style="display: flex; gap: 10px; align-items: center;">
                <input type="date" id="exportStartDate" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; flex: 1;">
                <span>至</span>
                <input type="date" id="exportEndDate" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; flex: 1;">
            </div>
            <small style="color: #666; margin-top: 5px; display: block;">留空则导出全部时间的数据</small>
        </div>

        <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">区县筛选：</label>
            <select id="exportDistrict" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">全部区县</option>
                <option value="巨野县">巨野县</option>
                <option value="牡丹区">牡丹区</option>
                <option value="鲁西新区">鲁西新区</option>
                <option value="单县">单县</option>
                <option value="曹县">曹县</option>
                <option value="成武县">成武县</option>
                <option value="东明县">东明县</option>
                <option value="郓城县">郓城县</option>
                <option value="鄄城县">鄄城县</option>
                <option value="定陶区">定陶区</option>
            </select>
        </div>

        <div style="text-align: right;">
            <button type="button" onclick="closeExportModal()" style="padding: 8px 16px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
            <button type="button" onclick="executeExport()" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">导出</button>
        </div>
    </div>
</div>

<script>
function uploadFile() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const resultDiv = document.getElementById('uploadResult');
    
    if (!fileInput.files[0]) {
        alert('请选择文件');
        return;
    }
    
    const formData = new FormData(form);
    
    // 显示进度条
    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';
    resultDiv.style.display = 'none';
    
    // 模拟进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    fetch('/upload_sector_data', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        
        setTimeout(() => {
            progressDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            
            if (data.success) {
                resultDiv.className = 'alert alert-success';
                resultDiv.innerHTML = `
                    <h6>上传成功！</h6>
                    <p>${data.message.replace(/\n/g, '<br>')}</p>
                    ${data.details && data.details.error_details && data.details.error_details.length > 0 ? 
                        `<details>
                            <summary>错误详情 (${data.details.error_details.length}条)</summary>
                            <ul class="mb-0 mt-2">
                                ${data.details.error_details.map(error => `<li>${error}</li>`).join('')}
                            </ul>
                        </details>` : ''
                    }
                    ${data.details && data.details.duplicate_details && data.details.duplicate_details.length > 0 ? 
                        `<details>
                            <summary>重复记录 (${data.details.duplicate_details.length}条)</summary>
                            <ul class="mb-0 mt-2">
                                ${data.details.duplicate_details.map(dup => `<li>${dup}</li>`).join('')}
                            </ul>
                        </details>` : ''
                    }
                `;
                
                // 3秒后刷新页面
                setTimeout(() => {
                    location.reload();
                }, 3000);
            } else {
                resultDiv.className = 'alert alert-danger';
                resultDiv.innerHTML = `<h6>上传失败</h6><p>${data.message}</p>`;
            }
        }, 500);
    })
    .catch(error => {
        clearInterval(progressInterval);
        progressDiv.style.display = 'none';
        resultDiv.style.display = 'block';
        resultDiv.className = 'alert alert-danger';
        resultDiv.innerHTML = `<h6>上传失败</h6><p>网络错误: ${error.message}</p>`;
    });
}

function refreshStats() {
    location.reload();
}

function clearDatabase() {
    if (!confirm('确定要清空扇区数据库吗？此操作不可恢复！')) {
        return;
    }
    
    fetch('/clear_sector_database', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('数据库已清空');
            location.reload();
        } else {
            alert('清空失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('操作失败: ' + error.message);
    });
}

// 显示导出模态框
function showExportModal() {
    document.getElementById('exportModal').style.display = 'block';
}

// 关闭导出模态框
function closeExportModal() {
    document.getElementById('exportModal').style.display = 'none';
}

// 执行导出
function executeExport() {
    const startDate = document.getElementById('exportStartDate').value;
    const endDate = document.getElementById('exportEndDate').value;
    const district = document.getElementById('exportDistrict').value;

    // 构建导出URL
    let url = '/export_sector_data?';
    const params = [];

    if (startDate) params.push(`start_date=${startDate}`);
    if (endDate) params.push(`end_date=${endDate}`);
    if (district) params.push(`district=${district}`);

    url += params.join('&');

    // 下载文件
    window.location.href = url;

    // 关闭模态框
    closeExportModal();
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('exportModal');
    if (event.target == modal) {
        closeExportModal();
    }
}
</script>
{% endblock %}
