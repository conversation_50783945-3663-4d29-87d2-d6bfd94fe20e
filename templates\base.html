<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}中兴网管告警监控系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* 告警级别颜色 */
        .severity-critical { color: #dc3545; font-weight: bold; }
        .severity-major { color: #fd7e14; font-weight: bold; }
        .severity-minor { color: #ffc107; }
        .severity-warning { color: #20c997; }
        .severity-cleared { color: #6c757d; }

        /* 全局字体大小优化 */
        body {
            font-size: 13px;
        }

        /* 表格优化 */
        .table {
            font-size: 12px;
            margin-bottom: 0.5rem;
        }

        .table th {
            font-size: 12px;
            font-weight: 600;
            padding: 0.4rem;
            white-space: nowrap;
        }

        .table td {
            padding: 0.4rem;
            vertical-align: middle;
            font-size: 11px;
        }

        /* 表格响应式优化 */
        .table-responsive {
            max-height: 70vh;
            overflow-y: auto;
            font-size: 11px;
        }

        /* 卡片标题优化 */
        .card-header {
            font-size: 14px;
            font-weight: 600;
            padding: 0.5rem 0.75rem;
        }

        .card-body {
            padding: 0.75rem;
        }

        /* 按钮优化 */
        .btn {
            font-size: 12px;
            padding: 0.25rem 0.5rem;
        }

        .btn-sm {
            font-size: 11px;
            padding: 0.2rem 0.4rem;
        }

        /* 分页优化 */
        .pagination {
            font-size: 12px;
            margin-bottom: 0.5rem;
        }

        .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 12px;
        }

        /* 表单控件优化 */
        .form-control, .form-select {
            font-size: 12px;
            padding: 0.25rem 0.5rem;
        }

        /* 徽章优化 */
        .badge {
            font-size: 10px;
        }

        /* 监控日志优化 */
        #monitor-log {
            max-height: 250px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            display: none;
            line-height: 1.3;
        }

        /* 进度条优化 */
        .progress {
            height: 16px;
            font-size: 11px;
        }

        /* 告警详情链接优化 */
        .alarm-detail-link {
            font-size: 11px;
            text-decoration: none;
        }

        .alarm-detail-link:hover {
            text-decoration: underline;
        }

        /* 操作按钮优化 */
        .action-buttons .btn {
            font-size: 10px;
            padding: 0.15rem 0.3rem;
            margin: 0.1rem;
        }

        /* 紧凑布局 */
        .card {
            margin-bottom: 0.75rem;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container-fluid {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            .table {
                font-size: 10px;
            }

            .table th, .table td {
                padding: 0.25rem;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">🚨 告警监控系统</a>
        </div>
    </nav>

    <div class="container-fluid mt-2">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>