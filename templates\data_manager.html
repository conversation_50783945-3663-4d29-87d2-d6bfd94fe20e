<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理 - 菏泽数据提取工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border: none;
        }
        .btn-warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            border: none;
        }
        .preview-table {
            font-size: 0.9em;
        }
        .warning-text {
            color: #dc3545;
            font-weight: bold;
        }
        .success-text {
            color: #28a745;
            font-weight: bold;
        }
        .operation-log {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-database-gear"></i> 数据管理</h2>
                    <a href="/" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>

        <!-- 删除操作区域 -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-trash"></i> 数据删除操作</h5>
                    </div>
                    <div class="card-body">
                        <form id="deleteForm">
                            <!-- 删除类型选择 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">删除方式</label>
                                    <select class="form-select" id="deleteType" required>
                                        <option value="">请选择删除方式</option>
                                        <option value="date">按日期删除</option>
                                        <option value="grid">按网格删除</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">目标值</label>
                                    <!-- 日期选择区域 -->
                                    <div id="dateSelection" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label small">开始日期</label>
                                                <input type="date" class="form-control" id="startDate">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label small">结束日期</label>
                                                <input type="date" class="form-control" id="endDate">
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">快捷选择：</small>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('today')">今天</button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('yesterday')">昨天</button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('week')">最近一周</button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('month')">最近一月</button>
                                                <button type="button" class="btn btn-outline-secondary" onclick="setQuickDateRange('august')">8月份</button>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 网格选择区域 -->
                                    <div id="gridSelection" style="display: none;">
                                        <select class="form-select" id="gridValue">
                                            <option value="">请选择网格</option>
                                            <option value="牡丹区">牡丹区</option>
                                            <option value="东明县">东明县</option>
                                            <option value="郓城县">郓城县</option>
                                            <option value="鲁西新区">鲁西新区</option>
                                            <option value="曹县">曹县</option>
                                            <option value="定陶区">定陶区</option>
                                            <option value="单县">单县</option>
                                            <option value="巨野县">巨野县</option>
                                            <option value="鄄城县">鄄城县</option>
                                            <option value="成武县">成武县</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据类型选择 -->
                            <div class="mb-3">
                                <label class="form-label">要删除的数据类型 <small class="text-muted">（可单独选择，支持分别删除不同类型的数据）</small></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dailyData" value="daily">
                                            <label class="form-check-label" for="dailyData">
                                                <i class="bi bi-calendar-day text-info"></i> 日度数据
                                            </label>
                                            <small class="d-block text-muted">删除指定时间段的日度统计数据</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="monthlyData" value="monthly">
                                            <label class="form-check-label" for="monthlyData">
                                                <i class="bi bi-calendar-month text-warning"></i> 月度数据
                                            </label>
                                            <small class="d-block text-muted">删除指定时间段的月度统计数据</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="sectorData" value="sector">
                                            <label class="form-check-label" for="sectorData">
                                                <i class="bi bi-hdd-stack text-success"></i> 扇区数据
                                            </label>
                                            <small class="d-block text-muted">删除指定时间段的扇区明细数据</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectAllDataTypes()">
                                        <i class="bi bi-check-all"></i> 全选
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearAllDataTypes()">
                                        <i class="bi bi-x-square"></i> 清空
                                    </button>
                                </div>
                            </div>

                            <!-- 备份选项 -->
                            <div class="mb-3">
                                <div class="card border-info">
                                    <div class="card-body">
                                        <h6 class="card-title text-info">
                                            <i class="bi bi-info-circle"></i> 数据备份选项
                                        </h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="skipBackup" value="true" checked>
                                            <label class="form-check-label" for="skipBackup">
                                                <strong>跳过自动备份，直接删除</strong> <span class="badge bg-primary">推荐</span>
                                            </label>
                                        </div>
                                        <small class="text-muted">
                                            💡 <strong>默认跳过备份以提高删除效率。</strong><br>
                                            如需备份，请取消勾选此选项。删除前请确保已手动备份重要数据！
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-warning" id="previewBtn">
                                    <i class="bi bi-eye"></i> 预览删除
                                </button>
                                <button type="button" class="btn btn-danger" id="deleteBtn" disabled>
                                    <i class="bi bi-trash"></i> 确认删除
                                </button>
                            </div>
                        </form>

                        <!-- 预览结果 -->
                        <div id="previewResult" class="mt-4" style="display: none;">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">删除预览</h6>
                                </div>
                                <div class="card-body">
                                    <div id="previewContent"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作结果 -->
                        <div id="operationResult" class="mt-4" style="display: none;">
                            <div class="alert" id="resultAlert">
                                <div id="resultContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作历史 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clock-history"></i> 操作历史</h5>
                    </div>
                    <div class="card-body">
                        <div class="operation-log" id="operationHistory">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2">加载历史记录...</div>
                            </div>
                        </div>
                        <button class="btn btn-outline-primary btn-sm mt-2" id="refreshHistoryBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>警告：</strong> 此操作将永久删除数据，虽然系统会自动备份，但请谨慎操作！
                    </div>
                    <div id="confirmContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPreviewData = null;
        const confirmModal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOperationHistory();
            
            // 绑定事件
            document.getElementById('previewBtn').addEventListener('click', previewDelete);
            document.getElementById('deleteBtn').addEventListener('click', showConfirmModal);
            document.getElementById('confirmDeleteBtn').addEventListener('click', executeDelete);
            document.getElementById('refreshHistoryBtn').addEventListener('click', loadOperationHistory);
            
            // 删除类型改变时的处理
            document.getElementById('deleteType').addEventListener('change', function() {
                const dateSelection = document.getElementById('dateSelection');
                const gridSelection = document.getElementById('gridSelection');

                if (this.value === 'date') {
                    dateSelection.style.display = 'block';
                    gridSelection.style.display = 'none';
                    // 设置默认日期为今天
                    setQuickDate('today');
                } else if (this.value === 'grid') {
                    dateSelection.style.display = 'none';
                    gridSelection.style.display = 'block';
                } else {
                    dateSelection.style.display = 'none';
                    gridSelection.style.display = 'none';
                }
                // 清空预览结果
                hidePreviewResult();
            });

            // 日期和网格值改变时清空预览结果
            document.getElementById('startDate').addEventListener('change', hidePreviewResult);
            document.getElementById('endDate').addEventListener('change', hidePreviewResult);
            document.getElementById('gridValue').addEventListener('change', hidePreviewResult);
        });

        // 设置快捷日期范围
        function setQuickDateRange(type) {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');
            const today = new Date();
            let startDate = new Date();
            let endDate = new Date();

            switch(type) {
                case 'today':
                    startDate = new Date(today);
                    endDate = new Date(today);
                    break;
                case 'yesterday':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 1);
                    endDate = new Date(startDate);
                    break;
                case 'week':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 7);
                    endDate = new Date(today);
                    break;
                case 'month':
                    startDate = new Date(today);
                    startDate.setMonth(today.getMonth() - 1);
                    endDate = new Date(today);
                    break;
                case 'august':
                    // 2024年8月1日到8月31日
                    startDate = new Date(2024, 7, 1); // 月份从0开始，7表示8月
                    endDate = new Date(2024, 7, 31);
                    break;
            }

            // 格式化日期为 YYYY-MM-DD
            function formatDate(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            startDateInput.value = formatDate(startDate);
            endDateInput.value = formatDate(endDate);

            // 清空预览结果
            hidePreviewResult();
        }

        // 获取目标值
        function getTargetValue() {
            const deleteType = document.getElementById('deleteType').value;
            if (deleteType === 'date') {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                if (startDate && endDate) {
                    return `${startDate} 至 ${endDate}`;
                } else if (startDate) {
                    return startDate;
                } else {
                    return '';
                }
            } else if (deleteType === 'grid') {
                return document.getElementById('gridValue').value;
            }
            return '';
        }

        // 预览删除
        function previewDelete() {
            const deleteType = document.getElementById('deleteType').value;
            const targetValue = getTargetValue().trim();
            const dataTypes = getSelectedDataTypes();
            
            if (!deleteType || !targetValue) {
                showAlert('请填写完整的删除信息', 'warning');
                return;
            }
            
            if (dataTypes.length === 0) {
                showAlert('请至少选择一种数据类型', 'warning');
                return;
            }
            
            // 显示加载状态
            const previewBtn = document.getElementById('previewBtn');
            const originalText = previewBtn.innerHTML;
            previewBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 预览中...';
            previewBtn.disabled = true;
            
            fetch('/api/delete_preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    delete_type: deleteType,
                    target_value: targetValue,
                    data_types: dataTypes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showPreviewResult(data.data);
                    currentPreviewData = data.data;
                    document.getElementById('deleteBtn').disabled = false;
                } else {
                    showAlert(data.error, 'danger');
                    hidePreviewResult();
                }
            })
            .catch(error => {
                showAlert('预览失败：' + error.message, 'danger');
                hidePreviewResult();
            })
            .finally(() => {
                previewBtn.innerHTML = originalText;
                previewBtn.disabled = false;
            });
        }
        
        // 显示预览结果
        function showPreviewResult(data) {
            const previewDiv = document.getElementById('previewResult');
            const contentDiv = document.getElementById('previewContent');
            
            let html = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>删除方式：</strong> ${data.delete_type === 'date' ? '按日期删除' : '按网格删除'}
                    </div>
                    <div class="col-md-6">
                        <strong>目标值：</strong> ${data.target_value}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <strong>总计将删除：</strong> <span class="text-danger">${data.total_records}</span> 条记录
                    </div>
                </div>
            `;
            
            if (Object.keys(data.tables).length > 0) {
                html += '<div class="table-responsive"><table class="table table-sm preview-table">';
                html += '<thead><tr><th>数据类型</th><th>记录数</th><th>日期范围</th></tr></thead><tbody>';
                
                for (const [key, table] of Object.entries(data.tables)) {
                    html += `<tr>
                        <td>${table.name}</td>
                        <td><span class="badge bg-primary">${table.count}</span></td>
                        <td>${table.date_range}</td>
                    </tr>`;
                }
                
                html += '</tbody></table></div>';
            }
            
            if (data.warnings && data.warnings.length > 0) {
                html += '<div class="mt-3"><strong>警告信息：</strong><ul class="warning-text">';
                data.warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul></div>';
            }
            
            contentDiv.innerHTML = html;
            previewDiv.style.display = 'block';
        }
        
        // 隐藏预览结果
        function hidePreviewResult() {
            document.getElementById('previewResult').style.display = 'none';
            document.getElementById('deleteBtn').disabled = true;
            currentPreviewData = null;
        }
        
        // 显示确认模态框
        function showConfirmModal() {
            if (!currentPreviewData) {
                showAlert('请先预览删除操作', 'warning');
                return;
            }

            const skipBackup = document.getElementById('skipBackup').checked;
            const confirmContent = document.getElementById('confirmContent');

            // 获取选择的数据类型
            const selectedTypes = [];
            if (document.getElementById('dailyData').checked) selectedTypes.push('日度数据');
            if (document.getElementById('monthlyData').checked) selectedTypes.push('月度数据');
            if (document.getElementById('sectorData').checked) selectedTypes.push('扇区数据');

            let backupInfo = '';
            if (skipBackup) {
                backupInfo = '<div class="alert alert-warning"><strong>⚠️ 注意：</strong> 已选择跳过自动备份，请确保已手动备份重要数据！</div>';
            } else {
                backupInfo = '<p class="text-muted">系统将在删除前自动备份数据，备份ID将在操作完成后显示。</p>';
            }

            const dataTypesInfo = selectedTypes.length > 0 ?
                `<p><strong>数据类型：</strong> <span class="badge bg-info">${selectedTypes.join('</span> <span class="badge bg-info">')}</span></p>` :
                '<p class="text-warning"><strong>⚠️ 未选择任何数据类型</strong></p>';

            confirmContent.innerHTML = `
                <p><strong>删除方式：</strong> ${currentPreviewData.delete_type === 'date' ? '按日期删除' : '按网格删除'}</p>
                <p><strong>目标值：</strong> ${currentPreviewData.target_value}</p>
                ${dataTypesInfo}
                <p><strong>将删除：</strong> <span class="text-danger">${currentPreviewData.total_records}</span> 条记录</p>
                ${backupInfo}
            `;

            confirmModal.show();
        }
        
        // 执行删除
        function executeDelete() {
            const deleteType = document.getElementById('deleteType').value;
            const targetValue = getTargetValue().trim();
            const dataTypes = getSelectedDataTypes();
            const skipBackup = document.getElementById('skipBackup').checked;

            const confirmBtn = document.getElementById('confirmDeleteBtn');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 删除中...';
            confirmBtn.disabled = true;

            fetch('/api/delete_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    delete_type: deleteType,
                    target_value: targetValue,
                    data_types: dataTypes,
                    confirm: true,
                    skip_backup: skipBackup
                })
            })
            .then(response => response.json())
            .then(data => {
                confirmModal.hide();
                
                if (data.success) {
                    showOperationResult(data.message, data.details, 'success');
                    hidePreviewResult();
                    loadOperationHistory(); // 刷新操作历史
                } else {
                    showOperationResult(data.error, null, 'danger');
                }
            })
            .catch(error => {
                confirmModal.hide();
                showOperationResult('删除失败：' + error.message, null, 'danger');
            })
            .finally(() => {
                confirmBtn.innerHTML = originalText;
                confirmBtn.disabled = false;
            });
        }
        
        // 显示操作结果
        function showOperationResult(message, details, type) {
            const resultDiv = document.getElementById('operationResult');
            const alertDiv = document.getElementById('resultAlert');
            const contentDiv = document.getElementById('resultContent');

            alertDiv.className = `alert alert-${type}`;

            // 处理多行消息
            let html = `<div class="mb-2">${message.replace(/\n/g, '<br>')}</div>`;

            if (details && details.operation_details) {
                html += '<div class="mt-2"><strong>详细信息：</strong><ul>';
                details.operation_details.forEach(detail => {
                    html += `<li>${detail}</li>`;
                });
                html += '</ul></div>';
            }

            if (details && details.backup_warnings && details.backup_warnings.length > 0) {
                html += '<div class="mt-2 alert alert-warning"><strong>备份警告：</strong><ul class="mb-0">';
                details.backup_warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul></div>';
            }

            if (details && details.backup_ids && details.backup_ids.length > 0) {
                html += `<div class="mt-2"><strong>备份ID：</strong> <code>${details.backup_ids.join(', ')}</code></div>`;
            }

            if (details && details.skip_backup) {
                html += '<div class="mt-2 text-warning"><i class="bi bi-exclamation-triangle"></i> <strong>已跳过自动备份</strong></div>';
            }

            contentDiv.innerHTML = html;
            resultDiv.style.display = 'block';

            // 成功消息延长显示时间，警告消息不自动隐藏
            if (type === 'success' && (!details || !details.backup_warnings || details.backup_warnings.length === 0)) {
                setTimeout(() => {
                    resultDiv.style.display = 'none';
                }, 8000);
            }
        }
        
        // 获取选中的数据类型
        function getSelectedDataTypes() {
            const types = [];
            if (document.getElementById('dailyData').checked) types.push('daily');
            if (document.getElementById('monthlyData').checked) types.push('monthly');
            if (document.getElementById('sectorData').checked) types.push('sector');
            return types;
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            showOperationResult(message, null, type);
        }
        
        // 加载操作历史
        function loadOperationHistory() {
            const historyDiv = document.getElementById('operationHistory');
            
            fetch('/api/delete_history')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayOperationHistory(data.data);
                } else {
                    historyDiv.innerHTML = '<div class="text-danger">加载历史记录失败</div>';
                }
            })
            .catch(error => {
                historyDiv.innerHTML = '<div class="text-danger">加载历史记录失败：' + error.message + '</div>';
            });
        }
        
        // 显示操作历史
        function displayOperationHistory(history) {
            const historyDiv = document.getElementById('operationHistory');
            
            if (history.length === 0) {
                historyDiv.innerHTML = '<div class="text-muted">暂无操作记录</div>';
                return;
            }
            
            let html = '';
            history.forEach(record => {
                const statusClass = record.status === 'completed' ? 'success' : 'danger';
                const statusText = record.status === 'completed' ? '成功' : '失败';
                const date = new Date(record.created_at).toLocaleString('zh-CN');
                
                html += `
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="fw-bold">${record.operation_type === 'delete_by_date' ? '按日期删除' : '按网格删除'}</div>
                                <div class="text-muted small">${record.target_info}</div>
                                <div class="text-muted small">影响记录：${record.affected_records}条</div>
                                ${record.backup_id ? `<div class="text-muted small">备份ID：${record.backup_id}</div>` : ''}
                            </div>
                            <div class="text-end">
                                <span class="badge bg-${statusClass}">${statusText}</span>
                                <div class="text-muted small">${date}</div>
                            </div>
                        </div>
                        ${record.error_message ? `<div class="text-danger small mt-1">错误：${record.error_message}</div>` : ''}
                    </div>
                `;
            });
            
            historyDiv.innerHTML = html;
        }

        // 全选数据类型
        function selectAllDataTypes() {
            document.getElementById('dailyData').checked = true;
            document.getElementById('monthlyData').checked = true;
            document.getElementById('sectorData').checked = true;
        }

        // 清空数据类型选择
        function clearAllDataTypes() {
            document.getElementById('dailyData').checked = false;
            document.getElementById('monthlyData').checked = false;
            document.getElementById('sectorData').checked = false;
        }
    </script>
</body>
</html>
