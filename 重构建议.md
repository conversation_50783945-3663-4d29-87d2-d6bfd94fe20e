# 重复故障分析功能重构建议

## 问题总结

### 1. 代码重复问题
- 传统分析逻辑（auto/manual）和时间维度分析逻辑（week/month）存在约70%的代码重复
- 核心的故障小区历史分析逻辑完全相同
- min_occurrences判断逻辑在两处重复实现

### 2. 维护困难
- 修改核心逻辑需要在两个地方同时修改
- 容易出现逻辑不同步问题
- 新增最小重复次数选项需要确保两套逻辑都正确

## 重构方案

### 方案一：提取核心分析函数（推荐）

```python
def analyze_single_cell_repeated_outages(conn, grid, cell_name, current_outage, 
                                       start_date, target_date, min_occurrences):
    """
    分析单个故障小区的重复情况
    
    Args:
        conn: 数据库连接
        grid: 网格
        cell_name: 小区名称
        current_outage: 当前退服次数
        start_date: 分析起始日期
        target_date: 分析目标日期
        min_occurrences: 最小重复次数
    
    Returns:
        dict or None: 重复故障记录，如果不符合条件返回None
    """
    # 查询该小区的历史故障记录
    history_query = """
    SELECT date, SUM(outage_count) as total_outage_count
    FROM sector_outage_data
    WHERE grid = ? AND cell_name = ?
      AND date >= ? AND date <= ?
    GROUP BY date
    ORDER BY date
    """
    history_df = pd.read_sql_query(
        history_query, conn,
        params=[grid, cell_name, start_date, target_date]
    )
    
    if not history_df.empty:
        history_df = history_df.rename(columns={'total_outage_count': 'outage_count'})
    
    # 判断是否符合最小重复次数要求
    if len(history_df) >= min_occurrences:
        # 计算统计信息
        first_date = history_df['date'].min()
        last_date = history_df['date'].max()
        occurrence_count = len(history_df)
        total_outages = history_df['outage_count'].sum()
        avg_outages = total_outages / occurrence_count
        
        # 计算故障天数跨度
        first_dt = datetime.strptime(first_date, '%Y-%m-%d')
        last_dt = datetime.strptime(last_date, '%Y-%m-%d')
        span_days = (last_dt - first_dt).days + 1
        
        # 计算故障频率
        frequency = occurrence_count / span_days if span_days > 0 else 0
        
        # 生成分布文本
        distribution_text = generate_complete_distribution_text(history_df)
        
        # 获取基站编码
        base_station_query = """
        SELECT DISTINCT base_station_code
        FROM sector_outage_data
        WHERE grid = ? AND cell_name = ?
        LIMIT 1
        """
        base_station_result = pd.read_sql_query(base_station_query, conn, params=[grid, cell_name])
        base_station_code = base_station_result.iloc[0]['base_station_code'] if not base_station_result.empty else 'N/A'
        
        return {
            'grid': str(grid),
            'cell_name': str(cell_name),
            'base_station_code': str(base_station_code),
            'repeat_count': int(occurrence_count),
            'first_date': str(first_date),
            'last_date': str(last_date),
            'span_days': int(span_days),
            'total_outages': int(total_outages),
            'avg_outages': round(float(avg_outages), 1),
            'current_outage': int(current_outage),
            'frequency': round(float(frequency), 3),
            'fault_dates': [str(d) for d in history_df['date'].tolist()],
            'distribution_text': distribution_text
        }
    
    return None
```

## 重构优势

### 1. 消除代码重复
- 核心分析逻辑只需维护一份
- min_occurrences处理逻辑统一
- 减少70%的重复代码

### 2. 提高可维护性
- 修改核心逻辑只需在一个地方修改
- 新增最小重复次数选项无需额外开发
- 逻辑一致性得到保证

### 3. 易于扩展
- 新增分析模式只需在统一函数中添加分支
- 核心算法保持不变
- 便于单元测试

### 4. 保持向后兼容
- API接口保持不变
- 前端无需修改
- 现有功能完全兼容

## 实施建议

1. **分阶段重构**：先提取核心函数，再重构主函数
2. **充分测试**：确保所有min_occurrences选项都正常工作
3. **保留原函数**：重构期间保留原函数作为备份
4. **文档更新**：更新相关技术文档

## 结论

当前代码虽然功能正常，但存在明显的维护性问题。通过重构可以：
- ✅ 确保所有最小重复次数选项使用完全一致的逻辑
- ✅ 简化代码维护
- ✅ 提高代码质量
- ✅ 便于未来功能扩展
