<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格月度故障统计 - 菏泽移动网络故障分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 500px;
            margin: 20px 0;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .loading-spinner {
            display: none;
        }
        .analysis-results {
            display: none;
        }

        .district-badge {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .summary-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-bar-chart-line"></i> 菏泽移动网络故障分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house"></i> 首页
                </a>
                <a class="nav-link" href="/repeated_outages">
                    <i class="bi bi-arrow-repeat"></i> 重复故障分析
                </a>
                <a class="nav-link active" href="/grid_monthly_stats">
                    <i class="bi bi-graph-up"></i> 网格月度统计
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <h2 class="mb-3">
                        <i class="bi bi-graph-up"></i> 网格月度故障统计分析
                    </h2>
                    <p class="mb-0">分析各个网格（区县）按月份统计的故障次数趋势，支持多时间范围对比分析</p>
                </div>
            </div>
        </div>

        <!-- 参数设置 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 分析参数设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="timeRange" class="form-label">时间范围</label>
                                <select class="form-select" id="timeRange">
                                    <option value="6">最近6个月</option>
                                    <option value="12" selected>最近12个月</option>
                                    <option value="18">最近18个月</option>
                                    <option value="24">最近24个月</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate">
                                <small class="text-muted">留空则使用最新数据日期</small>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="button" class="btn btn-primary w-100" onclick="performAnalysis()">
                                    <i class="bi bi-play-circle"></i> 开始分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="row mb-4 loading-spinner d-none" id="loadingSpinner">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 mb-0">正在分析网格月度故障数据，请稍候...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析结果 -->
        <div class="analysis-results" id="analysisResults">
            <!-- 统计摘要 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card summary-table">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle"></i> 分析摘要
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row" id="summaryContent">
                                <!-- 动态生成摘要内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势图表 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i> 网格月度故障趋势图
                            </h5>
                            <div>
                                <button type="button" class="btn btn-success btn-sm" onclick="exportGridTrendsChart()" id="exportGridTrendsBtn" disabled>
                                    <i class="bi bi-download"></i> 导出网格趋势图
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网格排名 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-trophy"></i> 网格故障总数排名
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="totalRanking">
                                <!-- 动态生成排名内容 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-speedometer2"></i> 网格月均故障数
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="averageRanking">
                                <!-- 动态生成平均值内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script>
        let monthlyChart = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认结束日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('endDate').value = today;
        });

        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const results = document.getElementById('analysisResults');
            
            if (show) {
                spinner.classList.remove('d-none');
                results.style.display = 'none';
            } else {
                spinner.classList.add('d-none');
                results.style.display = 'block';
            }
        }

        function showAlert(message, type = 'danger') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        async function performAnalysis() {
            const timeRange = document.getElementById('timeRange').value;
            const endDate = document.getElementById('endDate').value;
            
            console.log('🔍 DEBUG: 开始网格月度统计分析', { timeRange, endDate });
            
            showLoading(true);
            
            try {
                const response = await fetch('/api/grid_monthly_stats', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        time_range: timeRange,
                        end_date: endDate || null
                    })
                });
                
                const result = await response.json();
                console.log('🔍 DEBUG: API响应', result);
                
                if (result.success) {
                    displayResults(result.data);

                    // 启用导出网格趋势图按钮
                    document.getElementById('exportGridTrendsBtn').disabled = false;

                    showAlert('网格月度统计分析完成！', 'success');
                } else {
                    showAlert(`分析失败: ${result.error}`);
                }
                
            } catch (error) {
                console.error('分析请求失败:', error);
                showAlert(`分析请求失败: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 导出网格趋势图
        async function exportGridTrendsChart() {
            try {
                console.log('🔍 DEBUG: 开始导出网格趋势图');

                // 获取当前分析参数
                const timeRange = document.getElementById('timeRange').value;
                const endDate = document.getElementById('endDate').value;

                // 显示加载状态
                const btn = document.getElementById('exportGridTrendsBtn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 生成中...';
                btn.disabled = true;

                // 调用后端API生成图片
                const response = await fetch('/api/export_grid_trends_chart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        time_range: timeRange,
                        end_date: endDate || null
                    })
                });

                if (response.ok) {
                    // 获取文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = '网格月度故障趋势图.png';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    // 下载文件
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    link.click();
                    window.URL.revokeObjectURL(url);

                    showAlert('网格趋势图导出成功！', 'success');
                } else {
                    const errorData = await response.json();
                    showAlert('导出失败: ' + (errorData.error || '未知错误'), 'danger');
                }

            } catch (error) {
                console.error('导出网格趋势图失败:', error);
                showAlert('导出失败: ' + error.message, 'danger');
            } finally {
                // 恢复按钮状态
                const btn = document.getElementById('exportGridTrendsBtn');
                btn.innerHTML = '<i class="bi bi-download"></i> 导出网格趋势图';
                btn.disabled = false;
            }
        }

        function displayResults(data) {
            console.log('🔍 DEBUG: 显示分析结果', data);
            
            // 显示摘要信息
            displaySummary(data.summary);
            
            // 显示趋势图
            displayTrendChart(data.chart_data);
            
            // 显示排名
            displayRankings(data.summary);
        }

        function displaySummary(summary) {
            const summaryContent = document.getElementById('summaryContent');
            summaryContent.innerHTML = `
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary">${summary.total_months}</h4>
                        <small class="text-muted">分析月份数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success">${summary.total_districts}</h4>
                        <small class="text-muted">网格数量</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">${summary.total_records}</h4>
                        <small class="text-muted">数据记录数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${summary.date_range.start} 至 ${summary.date_range.end}</h4>
                        <small class="text-muted">分析时间范围</small>
                    </div>
                </div>
            `;
        }

        function displayTrendChart(chartData) {
            const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
            
            // 销毁现有图表
            if (monthlyChart) {
                monthlyChart.destroy();
            }
            
            monthlyChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '各网格月度故障趋势对比',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '月份'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '故障次数'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        function displayRankings(summary) {
            // 显示总数排名
            const totalRanking = document.getElementById('totalRanking');
            let totalHtml = '';
            summary.district_totals.forEach((item, index) => {
                const badgeClass = index < 3 ? 'bg-danger' : index < 6 ? 'bg-warning' : 'bg-secondary';
                totalHtml += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <span class="badge ${badgeClass} me-2">${index + 1}</span>
                            ${item.district}
                        </span>
                        <strong>${item.fault_count}次</strong>
                    </div>
                `;
            });
            totalRanking.innerHTML = totalHtml;
            
            // 显示平均值排名
            const averageRanking = document.getElementById('averageRanking');
            let avgHtml = '';
            const sortedAvg = summary.district_averages.sort((a, b) => b.fault_count - a.fault_count);
            sortedAvg.forEach((item, index) => {
                const badgeClass = index < 3 ? 'bg-info' : index < 6 ? 'bg-primary' : 'bg-secondary';
                avgHtml += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <span class="badge ${badgeClass} me-2">${index + 1}</span>
                            ${item.district}
                        </span>
                        <strong>${item.fault_count}次/月</strong>
                    </div>
                `;
            });
            averageRanking.innerHTML = avgHtml;
        }
    </script>
</body>
</html>
