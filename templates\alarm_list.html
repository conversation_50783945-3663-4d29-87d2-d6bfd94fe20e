{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 定时爬取监控控制面板 -->
        <div class="card mb-3">
            <div class="card-header">�️ 定时爬取监控控制</div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-3 align-items-center">
                    <div>
                        <label for="monitor-interval">爬取间隔:</label>
                        <select id="monitor-interval" class="form-select" style="width: 120px; display: inline-block;">
                            <option value="1">1分钟</option>
                            <option value="2">2分钟</option>
                            <option value="5" selected>5分钟</option>
                            <option value="10">10分钟</option>
                            <option value="15">15分钟</option>
                            <option value="30">30分钟</option>
                        </select>
                    </div>
                    <div>
                        <button id="start-monitor" onclick="startMonitor()" class="btn btn-primary">开始定时爬取</button>
                        <button id="stop-monitor" onclick="stopMonitor()" class="btn btn-secondary" style="display: none;">停止定时爬取</button>
                        <button onclick="refreshData()" class="btn btn-secondary">立即刷新页面</button>
                        <button onclick="fetchFromWeb()" class="btn btn-warning">手动爬取一次</button>
                        <button onclick="clearLogs()" class="btn btn-outline-danger btn-sm">清空日志</button>
                    </div>
                    <div id="monitor-status" class="flex-fill text-muted">
                        <span id="status-text">⏸️ 定时爬取已停止</span>
                        <span id="countdown-text" class="ms-2"></span>
                    </div>
                </div>

                <!-- 监控日志 -->
                <div id="monitor-log" class="mt-3 p-2 bg-light rounded" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; display: none;">
                    <div class="fw-bold mb-1">监控日志:</div>
                    <div id="log-content"></div>
                </div>
            </div>
        </div>

        <!-- 筛选面板已删除 -->

        <!-- 告警导航 -->
        <div class="card mb-2">
            <div class="card-body" style="padding: 0.5rem;">
                <ul class="nav nav-pills nav-fill">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            📋 全部告警 ({{ total_count }} 条)
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/focus-config">
                            ⚙️ 重点关注配置
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 告警列表 -->
        <div class="card">
            <div id="alarm-list-header" class="card-header">
                📋 告警列表 (共 {{ total_count }} 条记录)
                {% if relation_stats %}
                    - 🔴根源{{ relation_stats.root_count }}条 🟡衍生{{ relation_stats.derived_count }}条 ⚪独立{{ relation_stats.independent_count }}条
                    {% if focus_enabled and relation_stats.focus_count > 0 %}
                        🎯重点{{ relation_stats.focus_count }}条
                    {% endif %}
                {% endif %}
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="alarm-table" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 15%;">告警名称</th>
                                <th style="width: 6%;">级别</th>
                                <th style="width: 6%;">确认状态</th>
                                <th style="width: 6%;">新增状态</th>
                                <th style="width: 8%;">考核持续时间</th>
                                <th style="width: 20%;">网元</th>
                                <th style="width: 10%;">IP地址</th>
                                <th style="width: 10%;">发生时间</th>
                                <th style="width: 8%;">类型</th>
                                <th style="width: 25%;">附加信息</th>
                                <th style="width: 6%;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alarm in alarms %}
                            <tr {% if alarm.is_new %}style="background-color: #fff3cd;"{% elif not alarm.is_active %}style="opacity: 0.6;"{% elif alarm.get('relationflag', 0) == 1 %}style="background-color: #ffe6e6; border-left: 4px solid #dc3545;"{% elif alarm.get('relationflag', 0) == 2 %}style="background-color: #fff8e1; border-left: 4px solid #ffc107;"{% elif alarm.is_focus %}style="background-color: #e7f3ff; border-left: 4px solid #007bff;"{% endif %}>
                                <td>
                                    {% if alarm.get('relationflag', 0) == 1 %}
                                        <span class="badge bg-danger me-1" title="根源告警 - 问题的根本原因">🔴 根源</span>
                                    {% elif alarm.get('relationflag', 0) == 2 %}
                                        <span class="badge bg-warning me-1" title="衍生告警 - 由其他告警引起">🟡 衍生</span>
                                    {% endif %}
                                    {% if alarm.is_focus %}
                                        <span class="badge bg-primary me-1" title="重点关注告警">🎯 重点</span>
                                    {% endif %}
                                    <a href="/alarm/{{ alarm.id }}" target="_blank" class="alarm-detail-link" title="{{ alarm.code_name or '未知' }}">{{ (alarm.code_name or '未知')[:50] }}{% if (alarm.code_name or '')|length > 50 %}...{% endif %}</a>
                                    {% if alarm.get('rootcount', 0) > 0 %}
                                        <small class="text-muted">(影响{{ alarm.get('rootcount', 0) }}个衍生告警)</small>
                                    {% endif %}
                                </td>
                                <td><span class="severity-{{ (alarm.perceived_severity_name or '').lower() }}">{{ alarm.perceived_severity_name or '未知' }}</span></td>
                                <td><span class="badge {% if alarm.ack_state_name == '已确认' %}bg-success{% else %}bg-warning{% endif %}">{{ alarm.ack_state_name or '未确认' }}</span></td>
                                <td>
                                    {% if alarm.is_baseline and alarm.is_active %}📊 基线
                                    {% elif alarm.is_new and alarm.is_active %}🆕 新的
                                    {% elif not alarm.is_new and alarm.is_active %}📍 持续
                                    {% else %}❌ 已清除{% endif %}
                                </td>
                                <td>{{ alarm.duration_text }}</td>
                                <td title="{{ alarm.me_name or '未知' }}">{{ (alarm.me_name or '未知')[:35] }}{% if (alarm.me_name or '')|length > 35 %}...{% endif %}</td>
                                <td>{{ alarm.ne_ip or '未知' }}</td>
                                <td>{{ alarm.alarm_raised_time_formatted }}</td>
                                <td>{{ alarm.alarm_type_name or '未知' }}</td>
                                <td title="{{ alarm.additional_text or '' }}">{{ alarm.additional_text_short or '无' }}</td>
                                <td><a href="/alarm/{{ alarm.id }}" target="_blank" class="btn btn-sm btn-outline-primary">详情</a></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 - AJAX版本，避免整页刷新 -->
                <nav id="pagination-nav">
                    {% if total_pages > 1 %}
                    <ul class="pagination justify-content-center">
                        {% if page > 1 %}
                        <li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage(1)">&laquo; 首页</a></li>
                        <li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage({{ page - 1 }})">&lsaquo; 上一页</a></li>
                        {% endif %}

                        {% for p in range(max(1, page - 2), min(total_pages + 1, page + 3)) %}
                        <li class="page-item {% if p == page %}active{% endif %}">
                            <a class="page-link" href="javascript:void(0)" onclick="loadPage({{ p }})">{{ p }}</a>
                        </li>
                        {% endfor %}

                        {% if page < total_pages %}
                        <li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage({{ page + 1 }})">下一页 &rsaquo;</a></li>
                        <li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage({{ total_pages }})">末页 &raquo;</a></li>
                        {% endif %}
                    </ul>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// AJAX监控功能
let monitorInterval = null;
let countdownInterval = null;
let isMonitoring = false;

// 分页状态管理
let currentPage = {{ page }};
let totalPages = {{ total_pages }};

// AJAX分页函数
function loadPage(page) {
    if (page < 1 || page > totalPages) return;

    currentPage = page;

    // 使用统一的API接口
    const apiUrl = `/api/alarms?page=${page}`;

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // 更新表格内容
            updateAlarmTable(data);

            // 更新分页导航
            updatePagination(data);

            // 更新标题显示的记录数
            const headerElement = document.getElementById('alarm-list-header');
            if (headerElement) {
                // 保持原有的统计信息显示，只更新总数
                const currentText = headerElement.textContent;
                if (currentText.includes('🔴根源')) {
                    // 保持统计信息，只更新总数
                    headerElement.textContent = currentText.replace(/共 \d+ 条记录/, `共 ${data.total_count} 条记录`);
                } else {
                    headerElement.textContent = `📋 告警列表 (共 ${data.total_count} 条记录)`;
                }
            }

            // 更新全局变量
            currentPage = data.page;
            totalPages = data.total_pages;

            // 更新浏览器URL（可选，不会触发页面刷新）
            const newUrl = `${window.location.pathname}?page=${page}`;
            window.history.pushState({page: page}, '', newUrl);
        })
        .catch(error => {
            console.error('加载页面失败:', error);
            addLog(`❌ 加载第${page}页失败: ${error.message}`);
        });
}

// 监控状态持久化函数
function saveMonitorState() {
    const state = {
        isMonitoring: isMonitoring,
        interval: document.getElementById('monitor-interval').value,
        startTime: isMonitoring ? Date.now() : null
    };
    localStorage.setItem('monitorState', JSON.stringify(state));
}

function loadMonitorState() {
    const savedState = localStorage.getItem('monitorState');
    if (savedState) {
        try {
            const state = JSON.parse(savedState);
            if (state.isMonitoring) {
                // 恢复监控间隔设置
                document.getElementById('monitor-interval').value = state.interval;

                // 真正恢复监控（包括定时器），使用静默模式避免重复日志和爬取
                startMonitor(true, true);
                addLog('🔄 已恢复监控状态');
            }
        } catch (e) {
            console.error('恢复监控状态失败:', e);
            clearMonitorState();
        }
    }
}

function clearMonitorState() {
    localStorage.removeItem('monitorState');
}

// checkBackendMonitorStatus 函数已删除，不再需要后台状态检查

function updateMonitorUI() {
    const startBtn = document.getElementById('start-monitor');
    const stopBtn = document.getElementById('stop-monitor');
    const statusText = document.getElementById('status-text');

    if (isMonitoring) {
        startBtn.style.display = 'none';
        stopBtn.style.display = 'inline-block';
        statusText.textContent = '🔄 定时爬取运行中';
    } else {
        startBtn.style.display = 'inline-block';
        stopBtn.style.display = 'none';
        statusText.textContent = '⏸️ 定时爬取已停止';
    }
}

function addLog(message) {
    const logContainer = document.getElementById('monitor-log');
    const logContent = document.getElementById('log-content');

    if (!logContainer || !logContent) return;

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    logEntry.style.marginBottom = '2px';

    logContent.appendChild(logEntry);
    logContainer.style.display = 'block';

    logContainer.scrollTop = logContainer.scrollHeight;

    const logs = logContent.children;
    if (logs.length > 50) {
        logContent.removeChild(logs[0]);
    }

    // 保存日志到localStorage
    saveLogsToStorage();
}

function saveLogsToStorage() {
    const logContent = document.getElementById('log-content');
    if (logContent) {
        localStorage.setItem('monitorLogs', logContent.innerHTML);
    }
}

function loadLogsFromStorage() {
    const logContent = document.getElementById('log-content');
    const savedLogs = localStorage.getItem('monitorLogs');
    if (logContent && savedLogs) {
        logContent.innerHTML = savedLogs;
        const logContainer = document.getElementById('monitor-log');
        if (logContainer) {
            logContainer.style.display = 'block';
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }
}

function clearLogs() {
    const logContent = document.getElementById('log-content');
    if (logContent) {
        logContent.innerHTML = '';
        localStorage.removeItem('monitorLogs');
        const logContainer = document.getElementById('monitor-log');
        if (logContainer) {
            logContainer.style.display = 'none';
        }
    }
}

function startMonitor(skipFirstLog = false, skipFirstFetch = false) {
    if (isMonitoring) return;

    const interval = parseInt(document.getElementById('monitor-interval').value);
    const intervalMs = interval * 60 * 1000;

    isMonitoring = true;
    updateMonitorUI();
    saveMonitorState();  // 保存状态

    if (!skipFirstLog) {
        addLog(`🚀 开始定时爬取监控，间隔 ${interval} 分钟`);
    }

    // 根据参数决定是否立即执行第一次爬取
    if (!skipFirstFetch) {
        executeScheduledFetch(1);
    }

    // 设置定时器，每N分钟执行一次爬取
    let fetchCount = 1;
    monitorInterval = setInterval(() => {
        fetchCount++;
        addLog(`⏰ 定时爬取触发 (间隔${interval}分钟)`);
        executeScheduledFetch(fetchCount);
    }, intervalMs);

    startCountdown(interval * 60);
}

function executeScheduledFetch(fetchCount) {
    addLog(`🕷️ 第${fetchCount}次定时爬取开始...`);

    // 检查是否有爬取任务正在进行
    fetch('/api/fetch/status')
        .then(response => response.json())
        .then(status => {
            if (status.is_running) {
                addLog('⚠️ 上次爬取任务仍在进行中，跳过本次爬取');
                return;
            }

            // 启动新的爬取任务
            fetch('/api/fetch', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('✅ 爬取任务已启动');
                        // 开始监控这次爬取的进度
                        monitorCurrentFetch();
                    } else {
                        addLog('❌ 启动爬取失败: ' + data.message);
                    }
                })
                .catch(error => {
                    addLog(`❌ 爬取请求失败: ${error.message}`);
                });
        })
        .catch(error => {
            addLog(`❌ 检查爬取状态失败: ${error.message}`);
        });
}

function stopMonitor() {
    if (!isMonitoring) return;

    isMonitoring = false;

    if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
    }

    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }

    updateMonitorUI();
    saveMonitorState();  // 保存状态
    document.getElementById('countdown-text').textContent = '';

    addLog('⏹️ 定时爬取监控已停止');
}

function monitorCurrentFetch() {
    let lastStep = '';
    let lastProgress = -1;

    const progressInterval = setInterval(() => {
        fetch('/api/fetch/status')
            .then(response => response.json())
            .then(status => {
                if (status.is_running) {
                    // 只在步骤变化或进度有显著变化时才显示日志
                    const currentStepKey = `${status.current_step}_${status.progress}`;
                    const lastStepKey = `${lastStep}_${lastProgress}`;

                    if (currentStepKey !== lastStepKey) {
                        addLog(`📊 ${status.current_step} (${status.progress}%)`);
                        lastStep = status.current_step;
                        lastProgress = status.progress;
                    }
                } else {
                    // 抓取完成
                    clearInterval(progressInterval);

                    if (status.error) {
                        addLog(`❌ 爬取失败: ${status.error}`);
                    } else if (status.stats) {
                        const stats = status.stats;

                        if (stats.is_first_fetch) {
                            // 首次爬取的特殊显示
                            addLog(`� 首次爬取完成! 建立基线数据，共${stats.current_active_count}条告警`);
                            addLog(`📊 基线建立: 不统计新增/持续/消失状态，下次爬取将开始统计变化`);
                        } else {
                            // 正常爬取的显示
                            addLog(`�🎉 爬取完成! 网管当前${stats.current_active_count}条活跃告警`);
                            addLog(`📊 状态分析: 🆕新出现${stats.new_count}条, 📍持续存在${stats.continuing_count}条, ❌已消失${stats.cleared_count}条`);
                        }

                        if (status.start_time && status.end_time) {
                            const duration = (new Date(status.end_time) - new Date(status.start_time)) / 1000;
                            addLog(`⏱️ 本次爬取耗时: ${duration.toFixed(1)} 秒`);
                        }

                        // 爬取完成后刷新数据，保持监控状态
                        setTimeout(() => {
                            addLog('🔄 自动刷新页面数据...');
                            refreshPageData();
                        }, 1000);
                    }
                }
            })
            .catch(error => {
                console.error('获取抓取状态失败:', error);
                clearInterval(progressInterval);
            });
    }, 2000); // 每2秒检查一次进度
}

function refreshPageData() {
    addLog('🔄 自动刷新页面数据...');

    const currentParams = new URLSearchParams(window.location.search);
    const page = currentParams.get('page') || '1';

    // 检测当前是否为重点关注模式
    const isFocusMode = window.location.pathname === '/focus';
    const apiUrl = isFocusMode ? `/api/focus-alarms?page=${page}` : `/api/alarms?page=${page}`;

    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            updateAlarmTable(data);
            updatePagination(data);  // 同时更新分页

            // 同时更新标题显示的记录数
            const headerElement = document.getElementById('alarm-list-header');
            if (headerElement) {
                headerElement.textContent = `📋 告警列表 (共 ${data.total_count} 条记录)`;
            }

            addLog(`✅ 页面刷新完成，共 ${data.total_count} 条活跃告警`);

            // 显示下次爬取时间
            const interval = parseInt(document.getElementById('monitor-interval').value);
            const nextFetchTime = new Date(Date.now() + interval * 60 * 1000);
            addLog(`⏰ 下次爬取时间: ${nextFetchTime.toLocaleTimeString()}`);
        })
        .catch(error => {
            addLog(`❌ 页面刷新失败: ${error.message}`);
        });
}

// 保留原来的手动刷新功能（用于立即刷新按钮）
function refreshData() {
    refreshPageData();
}

function fetchFromWeb() {
    addLog('🕷️ 手动爬取: 开始从网管系统抓取数据...');

    fetch('/api/fetch', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLog('✅ ' + data.message);
                // 使用与定时爬取相同的监控逻辑
                monitorCurrentFetch();
            } else {
                addLog('❌ ' + data.message);
            }
        })
        .catch(error => {
            addLog(`❌ 抓取请求失败: ${error.message}`);
        });
}

// 旧的monitorFetchProgress函数已删除，统一使用monitorCurrentFetch

// 错误的updateAlarmTable函数已禁用

function updateAlarmTable_BROKEN(data) {
    const tbody = document.querySelector('#alarm-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    data.alarms.forEach(alarm => {
        fetch('/api/fetch/status')
            .then(response => response.json())
            .then(status => {
                // 显示当前步骤和进度
                if (status.is_running) {
                    addLog(`� ${status.current_step} (${status.progress}%)`);

                    // 显示新的日志条目
                    if (status.logs && status.logs.length > 0) {
                        const lastLog = status.logs[status.logs.length - 1];
                        // 避免重复显示相同的日志
                        const logContent = document.getElementById('log-content');
                        const lastLogElement = logContent.lastElementChild;
                        if (!lastLogElement || lastLogElement.textContent !== lastLog) {
                            // 只显示新的日志条目，不重复显示
                            const newLogs = status.logs.slice(-3); // 显示最近3条
                            newLogs.forEach(log => {
                                if (!logContent.textContent.includes(log)) {
                                    addLog(log.replace(/^\[\d{2}:\d{2}:\d{2}\]\s*/, '')); // 移除时间戳前缀，避免重复
                                }
                            });
                        }
                    }
                } else {
                    // 抓取完成
                    clearInterval(progressInterval);

                    if (status.error) {
                        addLog(`❌ 抓取失败: ${status.error}`);
                    } else if (status.stats) {
                        const stats = status.stats;
                        addLog(`🎉 抓取完成! 🆕新的${stats.new_count}条, 📍还在的${stats.continuing_count}条, ❌没了${stats.cleared_count}条`);

                        // 抓取完成后刷新数据
                        setTimeout(() => {
                            addLog('🔄 刷新页面数据...');
                            refreshData();
                        }, 2000);
                    }
                }
            })
            .catch(error => {
                console.error('获取抓取状态失败:', error);
                clearInterval(progressInterval);
            });
    }, 2000); // 每2秒检查一次进度
}

function updateAlarmTable(data) {
    const tbody = document.querySelector('#alarm-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    data.alarms.forEach(alarm => {
        const row = createAlarmRow(alarm);
        tbody.appendChild(row);
    });
}

function updatePagination(data) {
    const nav = document.getElementById('pagination-nav');
    if (!nav) return;

    const page = data.page || 1;
    const totalPages = data.total_pages || 1;

    if (totalPages <= 1) {
        nav.innerHTML = '';
        return;
    }

    let html = '<ul class="pagination justify-content-center">';

    // 首页和上一页
    if (page > 1) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage(1)">&laquo; 首页</a></li>`;
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage(${page - 1})">&lsaquo; 上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);

    for (let p = startPage; p <= endPage; p++) {
        const activeClass = p === page ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="javascript:void(0)" onclick="loadPage(${p})">${p}</a></li>`;
    }

    // 下一页和末页
    if (page < totalPages) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage(${page + 1})">下一页 &rsaquo;</a></li>`;
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0)" onclick="loadPage(${totalPages})">末页 &raquo;</a></li>`;
    }

    html += '</ul>';
    nav.innerHTML = html;
}

function createAlarmRow(alarm) {
    const row = document.createElement('tr');

    // 修复状态判断逻辑
    if (alarm.is_new == 1 && alarm.is_active == 1) {
        row.style.backgroundColor = '#fff3cd'; // 新告警 - 黄色背景
    } else if (alarm.is_active == 0) {
        row.style.opacity = '0.6'; // 已清除告警 - 半透明
    } else if ((alarm.relationflag || 0) == 1) {
        row.style.backgroundColor = '#ffe6e6'; // 根源告警 - 红色背景
        row.style.borderLeft = '4px solid #dc3545'; // 红色左边框
    } else if ((alarm.relationflag || 0) == 2) {
        row.style.backgroundColor = '#fff8e1'; // 衍生告警 - 橙色背景
        row.style.borderLeft = '4px solid #ffc107'; // 橙色左边框
    } else if (alarm.is_focus) {
        row.style.backgroundColor = '#e7f3ff'; // 重点关注告警 - 蓝色背景
        row.style.borderLeft = '4px solid #007bff'; // 左边框
    }

    // 文本截断函数
    const truncateText = (text, maxLength) => {
        if (!text) return '未知';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    };

    row.innerHTML = `
        <td>
            ${(alarm.relationflag || 0) == 1 ? `<span class="badge bg-danger me-1" title="根源告警 - 问题的根本原因">🔴 根源</span>` : ''}
            ${(alarm.relationflag || 0) == 2 ? `<span class="badge bg-warning me-1" title="衍生告警 - 由其他告警引起">🟡 衍生</span>` : ''}
            ${alarm.is_focus && (alarm.relationflag || 0) == 0 ? `<span class="badge bg-primary me-1" title="重点关注告警">重点</span>` : ''}
            <a href="/alarm/${alarm.id}" target="_blank" class="alarm-detail-link" title="${alarm.code_name || '未知'}">${truncateText(alarm.code_name, 50)}</a>
            ${(alarm.rootcount || 0) > 0 ? `<small class="text-muted">(影响${alarm.rootcount || 0}个衍生告警)</small>` : ''}
        </td>
        <td><span class="severity-${(alarm.perceived_severity_name || '').toLowerCase()}">${alarm.perceived_severity_name || '未知'}</span></td>
        <td><span class="badge ${alarm.ack_state_name === '已确认' ? 'bg-success' : 'bg-warning'}">${alarm.ack_state_name || '未确认'}</span></td>
        <td>${alarm.is_baseline == 1 && alarm.is_active == 1 ? '📊 基线' : alarm.is_new == 1 && alarm.is_active == 1 ? '🆕 新的' : alarm.is_new == 0 && alarm.is_active == 1 ? '📍 持续' : '❌ 已清除'}</td>
        <td>${alarm.duration_text || '-'}</td>
        <td title="${alarm.me_name || '未知'}">${truncateText(alarm.me_name, 35)}</td>
        <td>${alarm.ne_ip || '未知'}</td>
        <td>${alarm.alarm_raised_time_formatted || '未知'}</td>
        <td>${truncateText(alarm.alarm_type_name, 8)}</td>
        <td title="${alarm.additional_text || ''}">${truncateText(alarm.additional_text, 60)}</td>
        <td class="action-buttons"><a href="/alarm/${alarm.id}" target="_blank" class="btn btn-sm btn-outline-primary">详情</a></td>
    `;

    return row;
}

function startCountdown(seconds) {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    let remaining = seconds;
    const countdownText = document.getElementById('countdown-text');

    function updateCountdown() {
        if (!isMonitoring) return;

        if (remaining <= 0) {
            remaining = seconds;
        }

        const minutes = Math.floor(remaining / 60);
        const secs = remaining % 60;
        countdownText.textContent = `下次刷新: ${minutes}:${secs.toString().padStart(2, '0')}`;
        remaining--;
    }

    updateCountdown();
    countdownInterval = setInterval(updateCountdown, 1000);
}

document.addEventListener('DOMContentLoaded', function() {
    // 先恢复保存的日志
    loadLogsFromStorage();

    // 恢复监控状态
    loadMonitorState();

    // 如果没有保存的日志，显示初始化信息
    const logContent = document.getElementById('log-content');
    if (!logContent || logContent.children.length === 0) {
        addLog('📱 定时爬取监控功能已加载');
        addLog('💡 使用说明: 选择间隔时间，点击"开始定时爬取"，系统将每N分钟自动从网管爬取最新数据');
    }
});

// 支持浏览器前进/后退按钮
window.addEventListener('popstate', function(event) {
    if (event.state && event.state.page) {
        // 不更新URL，直接加载页面内容
        const page = event.state.page;
        const apiUrl = `/api/alarms?page=${page}`;

        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                updateAlarmTable(data);
                updatePagination(data);

                const headerElement = document.getElementById('alarm-list-header');
                if (headerElement) {
                    headerElement.textContent = `📋 告警列表 (共 ${data.total_count} 条记录)`;
                }

                currentPage = data.page;
                totalPages = data.total_pages;
            })
            .catch(error => {
                console.error('加载页面失败:', error);
            });
    }
});
</script>
{% endblock %}