<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器配置 - 菏泽数据处理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-secondary {
            border-radius: 25px;
            padding: 10px 30px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .info-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-server me-2"></i>服务器配置</h3>
                        <p class="mb-0">配置Web服务器的网络访问设置</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- 当前状态信息 -->
                        <div class="info-card">
                            <h5><i class="fas fa-info-circle me-2"></i>当前状态</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>本机IP地址：</strong></p>
                                    <ul class="list-unstyled ms-3" id="ipAddressList">
                                        <li><i class="fas fa-spinner fa-spin me-2"></i>正在获取IP地址...</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>当前访问地址：</strong></p>
                                    <ul class="list-unstyled ms-3" id="accessUrlList">
                                        <li><i class="fas fa-home me-2"></i><a href="http://localhost:5000" target="_blank">http://localhost:5000</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 配置表单 -->
                        <form id="serverConfigForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="host" class="form-label">
                                            <i class="fas fa-server me-2"></i>监听主机
                                        </label>
                                        <select class="form-control" id="host" name="host" required>
                                            <option value="0.0.0.0">0.0.0.0 (所有网络接口)</option>
                                            <option value="127.0.0.1">127.0.0.1 (仅本机)</option>
                                        </select>
                                        <div class="form-text">
                                            选择0.0.0.0允许其他电脑通过IP访问，选择127.0.0.1仅允许本机访问
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="port" class="form-label">
                                            <i class="fas fa-plug me-2"></i>端口号
                                        </label>
                                        <input type="number" class="form-control" id="port" name="port" 
                                               min="1" max="65535" required>
                                        <div class="form-text">
                                            端口范围：1-65535，建议使用5000-9999
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>提示：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>修改配置后需要重启应用才能生效</li>
                                    <li>如果其他电脑无法访问，请检查防火墙设置</li>
                                    <li>建议在内网环境使用，避免暴露到公网</li>
                                </ul>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary me-3">
                                    <i class="fas fa-save me-2"></i>保存配置
                                </button>
                                <a href="/" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回首页
                                </a>
                            </div>
                        </form>

                        <!-- 防火墙设置说明 -->
                        <div class="mt-4">
                            <h5><i class="fas fa-shield-alt me-2"></i>防火墙设置</h5>
                            <div class="alert alert-warning">
                                <p><strong>如果其他电脑无法访问，请运行以下命令开放防火墙：</strong></p>
                                <code>netsh advfirewall firewall add rule name="Python Web App" dir=in action=allow protocol=TCP localport=5000</code>
                                <p class="mt-2 mb-0">或者在Windows防火墙中手动添加入站规则允许端口5000。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时获取当前配置
        document.addEventListener('DOMContentLoaded', function() {
            loadServerConfig();
        });

        // 加载服务器配置
        function loadServerConfig() {
            fetch('/api/get_server_config')
                .then(response => response.json())
                .then(data => {
                    // 设置表单值
                    document.getElementById('host').value = data.host || '0.0.0.0';
                    document.getElementById('port').value = data.port || 5000;

                    // 更新IP地址列表
                    updateIPAddresses(data.ip_addresses || ['127.0.0.1']);

                    // 更新访问地址列表
                    updateAccessUrls(data.ip_addresses || ['127.0.0.1'], data.current_port || 5000);

                    // 更新主机选择下拉框
                    updateHostOptions(data.ip_addresses || ['127.0.0.1'], data.host || '0.0.0.0');
                })
                .catch(error => {
                    console.error('加载配置失败:', error);
                    showAlert('加载配置失败', 'danger');
                });
        }

        // 更新IP地址列表
        function updateIPAddresses(ipAddresses) {
            const ipList = document.getElementById('ipAddressList');
            ipList.innerHTML = '';

            ipAddresses.forEach((ip, index) => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-network-wired me-2"></i>${ip}`;
                ipList.appendChild(li);
            });
        }

        // 更新访问地址列表
        function updateAccessUrls(ipAddresses, port) {
            const urlList = document.getElementById('accessUrlList');
            urlList.innerHTML = '';

            // 本地访问
            const localLi = document.createElement('li');
            localLi.innerHTML = `<i class="fas fa-home me-2"></i><a href="http://localhost:${port}" target="_blank">http://localhost:${port}</a>`;
            urlList.appendChild(localLi);

            // 网络访问
            ipAddresses.forEach(ip => {
                if (ip !== '127.0.0.1') {
                    const li = document.createElement('li');
                    li.innerHTML = `<i class="fas fa-globe me-2"></i><a href="http://${ip}:${port}" target="_blank">http://${ip}:${port}</a>`;
                    urlList.appendChild(li);
                }
            });
        }

        // 更新主机选择下拉框
        function updateHostOptions(ipAddresses, currentHost) {
            const hostSelect = document.getElementById('host');

            // 清除动态添加的选项（保留前两个固定选项）
            while (hostSelect.children.length > 2) {
                hostSelect.removeChild(hostSelect.lastChild);
            }

            // 添加IP地址选项
            ipAddresses.forEach(ip => {
                if (ip !== '127.0.0.1') {
                    const option = document.createElement('option');
                    option.value = ip;
                    option.textContent = `${ip} (指定网卡)`;
                    hostSelect.appendChild(option);
                }
            });

            // 设置当前值
            hostSelect.value = currentHost;
        }

        // 保存服务器配置
        document.getElementById('serverConfigForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const config = {
                host: formData.get('host'),
                port: parseInt(formData.get('port'))
            };

            fetch('/api/save_server_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('保存配置失败:', error);
                showAlert('保存配置失败', 'danger');
            });
        });

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const form = document.getElementById('serverConfigForm');
            form.parentNode.insertBefore(alertDiv, form);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
