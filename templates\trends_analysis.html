<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势分析 - 菏泽数据处理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .analysis-tabs {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .filter-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner-border {
            color: #28a745;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
        }
        
        .district-selector {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
        }
        
        .form-check {
            margin-bottom: 8px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .chart-actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn-outline-success {
            border-radius: 20px;
            margin: 0 5px;
        }

        .chart-section {
            margin-bottom: 30px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .district-chart-container {
            position: relative;
            height: 300px;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .district-chart-title {
            font-size: 14px;
            font-weight: bold;
            color: #2E5BBA;
            text-align: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #e9ecef;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container-fluid py-4">
        <!-- 导航栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="bi bi-graph-up"></i>
                                趋势分析
                            </h4>
                            <div>
                                <a href="/" class="btn btn-outline-light me-2">
                                    <i class="bi bi-house"></i> 返回首页
                                </a>
                                <a href="/database_manager" class="btn btn-outline-light">
                                    <i class="bi bi-database"></i> 数据管理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析类型选择 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="analysis-tabs">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart-line"></i>
                        分析类型
                    </h5>
                    <ul class="nav nav-pills" id="analysisTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="daily-tab" data-bs-toggle="pill" data-bs-target="#daily" type="button" role="tab">
                                <i class="bi bi-calendar-day"></i> 日度趋势
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="monthly-tab" data-bs-toggle="pill" data-bs-target="#monthly" type="button" role="tab">
                                <i class="bi bi-calendar-month"></i> 月度趋势
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="comparison-tab" data-bs-toggle="pill" data-bs-target="#comparison" type="button" role="tab">
                                <i class="bi bi-bar-chart"></i> 区县对比
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="target-tab" data-bs-toggle="pill" data-bs-target="#target" type="button" role="tab">
                                <i class="bi bi-bullseye"></i> 目标分析
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-section">
                    <h5 class="mb-3">
                        <i class="bi bi-funnel"></i>
                        筛选条件
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">快速选择</label>
                            <div class="btn-group d-block" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickDateRange(7)">
                                    最近7天
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickDateRange(15)">
                                    最近15天
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickDateRange(30)">
                                    最近30天
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickDateRange(90)">
                                    最近90天
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">选择区县</label>
                            <div class="district-selector" id="districtSelector">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll" checked>
                                    <label class="form-check-label fw-bold" for="selectAll">
                                        全选
                                    </label>
                                </div>
                                <hr>
                                <!-- 区县选项将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <button class="btn btn-primary me-2" onclick="generateAnalysis()">
                                <i class="bi bi-graph-up"></i> 生成分析
                            </button>
                            <button class="btn btn-outline-secondary me-2" onclick="resetFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 重置条件
                            </button>
                            <button class="btn btn-outline-info" onclick="loadLatestData()">
                                <i class="bi bi-arrow-down-circle"></i> 获取最新数据
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch float-end">
                                <input class="form-check-input" type="checkbox" id="autoRefresh">
                                <label class="form-check-label" for="autoRefresh">
                                    <i class="bi bi-arrow-repeat"></i> 自动刷新 (30秒)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表显示区域 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up"></i>
                            <span id="chartTitle">趋势分析图表</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="loading" id="loadingIndicator">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <p class="mt-2">正在生成趋势分析...</p>
                        </div>
                        
                        <div id="chartContainer" style="display: none;">
                            <!-- 总计图表 -->
                            <div class="chart-section mb-4">
                                <div class="chart-title">
                                    <i class="bi bi-graph-up"></i>
                                    总计趋势图
                                </div>
                                <div class="chart-container">
                                    <canvas id="totalChart"></canvas>
                                </div>
                            </div>

                            <!-- 区县图表网格 -->
                            <div class="chart-section">
                                <div class="chart-title">
                                    <i class="bi bi-geo-alt"></i>
                                    各区县趋势图
                                </div>
                                <div class="chart-grid" id="districtChartsGrid">
                                    <!-- 动态生成区县图表 -->
                                </div>
                            </div>

                            <div class="chart-actions mt-4">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary" onclick="exportChart('png')">
                                        <i class="bi bi-image"></i> 导出PNG
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="exportChart('pdf')">
                                        <i class="bi bi-file-earmark-pdf"></i> 导出PDF
                                    </button>
                                    <button class="btn btn-outline-success" onclick="exportChart('excel')">
                                        <i class="bi bi-file-earmark-excel"></i> 导出Excel
                                    </button>
                                </div>
                                <button class="btn btn-outline-secondary ms-3" onclick="downloadAllCharts()">
                                    <i class="bi bi-download"></i> 下载所有图表
                                </button>
                            </div>
                        </div>
                        
                        <div id="noDataMessage" class="text-center p-4 text-muted">
                            <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                            <p class="mt-2">请选择分析条件并点击"生成分析"按钮</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="row mt-4" id="statsContainer" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart-line"></i>
                            统计摘要
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 总体统计 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="bi bi-graph-up"></i>
                                    总体统计
                                </h6>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalDataPoints">-</div>
                                    <div class="stats-label">数据点数</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalTimeSpan">-</div>
                                    <div class="stats-label">时间跨度</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalMaxValue">-</div>
                                    <div class="stats-label">最大值</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalMinValue">-</div>
                                    <div class="stats-label">最小值</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalAvgValue">-</div>
                                    <div class="stats-label">平均值</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stats-card">
                                    <div class="stats-number" id="totalStdValue">-</div>
                                    <div class="stats-label">标准差</div>
                                </div>
                            </div>
                        </div>

                        <!-- 区县统计详情 -->
                        <div class="row" id="districtStatsContainer" style="display: none;">
                            <div class="col-12">
                                <h6 class="text-success mb-3">
                                    <i class="bi bi-geo-alt"></i>
                                    区县统计详情
                                </h6>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>区县</th>
                                                <th>数据点数</th>
                                                <th>最大值</th>
                                                <th>最小值</th>
                                                <th>平均值</th>
                                                <th>标准差</th>
                                                <th>总计</th>
                                            </tr>
                                        </thead>
                                        <tbody id="districtStatsTable">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentChart = null;
        let currentData = null;
        let autoRefreshInterval = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDistricts();
            setDefaultDates();
            initializeAutoRefresh();
        });

        // 设置默认日期（最近30天）
        function setDefaultDates() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
        }

        // 加载区县列表
        function loadDistricts() {
            fetch('/api/database/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.stats.districts) {
                        const container = document.getElementById('districtSelector');
                        const districts = data.stats.districts.all;
                        
                        // 清空现有内容（保留全选）
                        const selectAllHtml = container.innerHTML;
                        
                        let html = selectAllHtml;
                        
                        districts.forEach(district => {
                            html += `
                                <div class="form-check">
                                    <input class="form-check-input district-checkbox" type="checkbox" id="district_${district}" value="${district}" checked>
                                    <label class="form-check-label" for="district_${district}">
                                        ${district}
                                    </label>
                                </div>
                            `;
                        });
                        
                        container.innerHTML = html;
                        
                        // 绑定全选事件
                        document.getElementById('selectAll').addEventListener('change', function() {
                            const checkboxes = document.querySelectorAll('.district-checkbox');
                            checkboxes.forEach(cb => cb.checked = this.checked);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载区县列表失败:', error);
                });
        }

        // 生成分析
        function generateAnalysis() {
            const activeTab = document.querySelector('.nav-link.active').id;
            let analysisType = 'daily';
            
            switch(activeTab) {
                case 'daily-tab':
                    analysisType = 'daily';
                    break;
                case 'monthly-tab':
                    analysisType = 'monthly';
                    break;
                case 'comparison-tab':
                    analysisType = 'district_comparison';
                    break;
                case 'target-tab':
                    analysisType = 'target_analysis';
                    break;
            }
            
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const selectedDistricts = Array.from(document.querySelectorAll('.district-checkbox:checked')).map(cb => cb.value);
            
            if (!startDate || !endDate) {
                showError('请选择开始和结束日期');
                return;
            }
            
            if (selectedDistricts.length === 0) {
                showError('请至少选择一个区县');
                return;
            }
            
            // 显示加载指示器
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('chartContainer').style.display = 'none';
            document.getElementById('noDataMessage').style.display = 'none';
            document.getElementById('statsContainer').style.display = 'none';
            
            const requestData = {
                type: analysisType,
                start_date: startDate,
                end_date: endDate,
                districts: selectedDistricts
            };
            
            fetch('/api/trends/analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingIndicator').style.display = 'none';
                
                if (data.success) {
                    currentData = data.data;
                    displayChart(data.data, analysisType);
                    updateStats(data.data);
                } else {
                    showError('分析失败: ' + data.error);
                }
            })
            .catch(error => {
                document.getElementById('loadingIndicator').style.display = 'none';
                showError('分析失败: ' + error.message);
            });
        }

        // 显示图表
        function displayChart(data, analysisType) {
            // 销毁现有图表
            destroyAllCharts();

            if (analysisType === 'daily') {
                displayMultipleCharts(data);
            } else {
                displaySingleChart(data, analysisType);
            }

            // 显示图表容器
            document.getElementById('chartContainer').style.display = 'block';
            document.getElementById('chartTitle').textContent = data.title;
        }

        // 显示多个图表（总计 + 各区县）
        function displayMultipleCharts(data) {
            // 1. 创建总计图表
            createTotalChart(data);

            // 2. 创建区县图表网格
            createDistrictCharts(data);
        }

        // 创建总计图表
        function createTotalChart(data) {
            const ctx = document.getElementById('totalChart').getContext('2d');

            // 提取总计数据和目标数据
            const totalDataset = data.datasets.find(d => d.label === '总体退服数');
            const targetDataset = data.datasets.find(d => d.label === '总体目标');

            const chartData = {
                labels: data.labels,
                datasets: [totalDataset, targetDataset].filter(d => d)
            };

            const totalChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '总计退服趋势',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '退服次数'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });

            // 保存图表引用
            if (!window.allCharts) window.allCharts = [];
            window.allCharts.push(totalChart);
        }

        // 创建区县图表网格
        function createDistrictCharts(data) {
            const gridContainer = document.getElementById('districtChartsGrid');
            gridContainer.innerHTML = ''; // 清空现有内容

            // 获取区县数据集（排除总计和目标线）
            const districtDatasets = data.datasets.filter(d =>
                !d.label.includes('总体') && !d.label.includes('目标')
            );

            districtDatasets.forEach((dataset, index) => {
                // 创建图表容器
                const chartDiv = document.createElement('div');
                chartDiv.className = 'district-chart-container';
                chartDiv.innerHTML = `
                    <div class="district-chart-title">${dataset.label}</div>
                    <canvas id="districtChart_${index}"></canvas>
                `;
                gridContainer.appendChild(chartDiv);

                // 创建图表
                const ctx = document.getElementById(`districtChart_${index}`).getContext('2d');

                // 计算该区县的目标数据
                const districtTargets = calculateDistrictTarget(dataset.label, data.labels);

                const chartData = {
                    labels: data.labels,
                    datasets: [
                        {
                            ...dataset,
                            borderWidth: 3,
                            pointRadius: 3
                        },
                        {
                            label: '目标值',
                            data: districtTargets,
                            borderColor: '#dc3545',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            fill: false,
                            pointRadius: 0
                        }
                    ]
                };

                const districtChart = new Chart(ctx, {
                    type: 'line',
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    boxWidth: 12,
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '退服次数',
                                    font: {
                                        size: 10
                                    }
                                },
                                ticks: {
                                    font: {
                                        size: 9
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        size: 9
                                    },
                                    maxRotation: 45
                                }
                            }
                        }
                    }
                });

                // 保存图表引用
                if (!window.allCharts) window.allCharts = [];
                window.allCharts.push(districtChart);
            });
        }

        // 显示单个图表（非日度趋势）
        function displaySingleChart(data, analysisType) {
            const ctx = document.getElementById('totalChart').getContext('2d');

            // 隐藏区县图表网格
            document.getElementById('districtChartsGrid').innerHTML = '';

            // 确定图表类型
            let chartType = 'line';
            if (analysisType === 'district_comparison') {
                chartType = 'bar';
            }

            const chart = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: data.title,
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: data.yAxisLabel
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: analysisType === 'district_comparison' ? '区县' : '时间'
                            }
                        }
                    }
                }
            });

            // 保存图表引用
            if (!window.allCharts) window.allCharts = [];
            window.allCharts.push(chart);
        }

        // 计算区县目标值
        function calculateDistrictTarget(districtName, dates) {
            const targets = [];

            dates.forEach(date => {
                const dateObj = new Date(date);
                const month = dateObj.getMonth() + 1;
                const daysInMonth = new Date(dateObj.getFullYear(), month, 0).getDate();

                // 这里使用简化的目标计算，实际应该从后端获取
                const monthlyTarget = getMonthlyTarget(districtName, month);
                const dailyTarget = Math.round(monthlyTarget / daysInMonth);
                targets.push(dailyTarget);
            });

            return targets;
        }

        // 获取月度目标（简化版本）
        function getMonthlyTarget(districtName, month) {
            // 这里使用固定值，实际应该从后端获取
            const defaultTargets = {
                '牡丹区': 1920, '东明县': 991, '郓城县': 985, '鲁西新区': 1592,
                '曹县': 1454, '定陶区': 702, '单县': 1131, '巨野县': 929,
                '鄄城县': 709, '成武县': 699
            };

            return defaultTargets[districtName] || 500;
        }

        // 销毁所有图表
        function destroyAllCharts() {
            if (window.allCharts) {
                window.allCharts.forEach(chart => {
                    if (chart) chart.destroy();
                });
                window.allCharts = [];
            }

            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }
        }

        // 更新统计信息
        function updateStats(data) {
            if (!data.datasets || data.datasets.length === 0) return;

            // 计算总体统计
            let allValues = [];
            data.datasets.forEach(dataset => {
                if (dataset.label !== '总体目标' && !dataset.label.includes('目标')) {
                    allValues = allValues.concat(dataset.data.filter(v => v !== null && v !== undefined));
                }
            });

            if (allValues.length === 0) return;

            const maxValue = Math.max(...allValues);
            const minValue = Math.min(...allValues);
            const avgValue = (allValues.reduce((a, b) => a + b, 0) / allValues.length);
            const stdValue = calculateStandardDeviation(allValues);
            const dataPoints = allValues.length;
            const timeSpan = data.labels ? `${data.labels.length} 天` : '-';

            // 更新总体统计
            document.getElementById('totalDataPoints').textContent = dataPoints;
            document.getElementById('totalTimeSpan').textContent = timeSpan;
            document.getElementById('totalMaxValue').textContent = maxValue;
            document.getElementById('totalMinValue').textContent = minValue;
            document.getElementById('totalAvgValue').textContent = avgValue.toFixed(2);
            document.getElementById('totalStdValue').textContent = stdValue.toFixed(2);

            // 更新区县统计详情
            updateDistrictStats(data);

            document.getElementById('statsContainer').style.display = 'block';
        }

        // 计算标准差
        function calculateStandardDeviation(values) {
            if (values.length <= 1) return 0;

            const mean = values.reduce((a, b) => a + b, 0) / values.length;
            const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
            const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;

            return Math.sqrt(avgSquaredDiff);
        }

        // 更新区县统计详情
        function updateDistrictStats(data) {
            const districtStatsTable = document.getElementById('districtStatsTable');
            const districtStatsContainer = document.getElementById('districtStatsContainer');

            if (!data.datasets || data.datasets.length <= 2) {
                districtStatsContainer.style.display = 'none';
                return;
            }

            let tableHtml = '';

            data.datasets.forEach(dataset => {
                // 跳过目标线和总体数据
                if (dataset.label.includes('目标') || dataset.label === '总体退服数') {
                    return;
                }

                const values = dataset.data.filter(v => v !== null && v !== undefined);
                if (values.length === 0) return;

                const max = Math.max(...values);
                const min = Math.min(...values);
                const avg = values.reduce((a, b) => a + b, 0) / values.length;
                const std = calculateStandardDeviation(values);
                const total = values.reduce((a, b) => a + b, 0);

                tableHtml += `
                    <tr>
                        <td><strong>${dataset.label}</strong></td>
                        <td>${values.length}</td>
                        <td class="text-danger">${max}</td>
                        <td class="text-success">${min}</td>
                        <td class="text-primary">${avg.toFixed(2)}</td>
                        <td class="text-warning">${std.toFixed(2)}</td>
                        <td class="text-info"><strong>${total}</strong></td>
                    </tr>
                `;
            });

            districtStatsTable.innerHTML = tableHtml;
            districtStatsContainer.style.display = 'block';
        }

        // 下载图表
        function downloadChart(format) {
            if (!window.allCharts || window.allCharts.length === 0) {
                showError('没有可下载的图表');
                return;
            }

            // 下载第一个图表（总计图表）
            const chart = window.allCharts[0];
            const link = document.createElement('a');
            link.download = `总计趋势图_${new Date().toISOString().split('T')[0]}.${format}`;

            if (format === 'png') {
                link.href = chart.toBase64Image();
            } else if (format === 'jpg') {
                link.href = chart.toBase64Image('image/jpeg', 0.8);
            }

            link.click();
        }

        // 下载所有图表
        function downloadAllCharts() {
            if (!window.allCharts || window.allCharts.length === 0) {
                showError('没有可下载的图表');
                return;
            }

            const timestamp = new Date().toISOString().split('T')[0];

            // 下载总计图表
            if (window.allCharts[0]) {
                const link1 = document.createElement('a');
                link1.download = `总计趋势图_${timestamp}.png`;
                link1.href = window.allCharts[0].toBase64Image();
                link1.click();
            }

            // 延迟下载区县图表，避免浏览器阻止多个下载
            window.allCharts.slice(1).forEach((chart, index) => {
                setTimeout(() => {
                    const link = document.createElement('a');
                    link.download = `区县趋势图_${index + 1}_${timestamp}.png`;
                    link.href = chart.toBase64Image();
                    link.click();
                }, (index + 1) * 500); // 每500ms下载一个
            });

            showSuccess(`开始下载 ${window.allCharts.length} 个图表文件`);
        }

        // 导出图表（服务器端生成）
        function exportChart(format) {
            if (!currentData) {
                showError('没有可导出的数据');
                return;
            }

            const activeTab = document.querySelector('.nav-link.active').id;
            let analysisType = 'daily';

            switch(activeTab) {
                case 'daily-tab':
                    analysisType = 'daily';
                    break;
                case 'monthly-tab':
                    analysisType = 'monthly';
                    break;
                case 'comparison-tab':
                    analysisType = 'district_comparison';
                    break;
                case 'target-tab':
                    analysisType = 'target_analysis';
                    break;
            }

            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
            button.disabled = true;

            const requestData = {
                format: format,
                chart_data: currentData,
                analysis_type: analysisType
            };

            fetch('/api/trends/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('导出失败');
                }
            })
            .then(blob => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                const timestamp = new Date().toISOString().split('T')[0];
                link.download = `趋势分析_${analysisType}_${timestamp}.${format}`;

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                showSuccess(`${format.toUpperCase()}文件导出成功！`);
            })
            .catch(error => {
                console.error('导出失败:', error);
                showError('导出失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // 重置筛选条件
        function resetFilters() {
            setDefaultDates();
            document.getElementById('selectAll').checked = true;
            document.querySelectorAll('.district-checkbox').forEach(cb => cb.checked = true);
        }

        // 显示成功消息
        function showSuccess(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // 显示错误消息
        function showError(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // 初始化自动刷新功能
        function initializeAutoRefresh() {
            const autoRefreshCheckbox = document.getElementById('autoRefresh');
            autoRefreshCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
        }

        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }

            autoRefreshInterval = setInterval(() => {
                if (currentData) {
                    console.log('自动刷新趋势分析...');
                    generateAnalysis();
                }
            }, 30000); // 30秒刷新一次

            showSuccess('自动刷新已启用 (30秒间隔)');
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            showSuccess('自动刷新已停用');
        }

        // 加载最新数据
        function loadLatestData() {
            // 设置结束日期为今天
            const today = new Date();
            document.getElementById('endDate').value = today.toISOString().split('T')[0];

            // 如果有当前数据，自动生成分析
            if (currentData) {
                generateAnalysis();
            } else {
                showSuccess('已更新到最新日期，请点击"生成分析"');
            }
        }

        // 快速日期选择
        function setQuickDateRange(days) {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);

            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];

            showSuccess(`已设置为最近${days}天`);
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
