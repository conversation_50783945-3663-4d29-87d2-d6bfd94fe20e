===============================================
便携版Python部署说明
===============================================

目录结构：
便携版部署/
├── python/          # 便携版Python解释器（需要下载）
├── app/             # 应用文件（将web_app.py等文件放在这里）
├── 启动工具.bat     # 启动脚本
└── 说明.txt         # 本文件

===============================================
部署步骤：
===============================================

1. 下载便携版Python：
   - 访问：https://www.python.org/downloads/windows/
   - 选择最新的Python 3.9+版本
   - 下载"Windows embeddable package (64-bit)"
   - 解压到"python"文件夹

2. 准备应用文件：
   - 将web_app.py复制到"app"文件夹
   - 将requirements.txt复制到"app"文件夹
   - 将uploads、attachments文件夹复制到"app"文件夹

3. 启动应用：
   - 双击"启动工具.bat"
   - 首次运行会自动安装依赖
   - 打开浏览器访问：http://127.0.0.1:5000

===============================================
注意事项：
===============================================

- Python embeddable版本不包含pip，启动脚本会自动处理
- 如遇到依赖安装问题，可以手动下载wheel文件安装
- 确保app文件夹包含所有必要的文件
- 网络环境需要能够访问PyPI或使用镜像源

===============================================
故障排除：
===============================================

问题1：提示"无法找到指定的模块"
解决：下载完整版Python而不是embeddable版本

问题2：依赖安装失败
解决：检查网络连接，尝试使用国内镜像源

问题3：启动后无法访问
解决：检查防火墙设置，确保端口5000未被占用 