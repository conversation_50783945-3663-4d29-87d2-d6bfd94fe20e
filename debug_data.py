import sqlite3

def check_sector_data():
    """检查8月1-5日的扇区数据"""
    conn = sqlite3.connect('heze_data.db')
    cursor = conn.cursor()
    
    print('=== 检查8月1-5日的扇区数据 ===')
    for date in ['2025-08-01', '2025-08-02', '2025-08-03', '2025-08-04', '2025-08-05']:
        cursor.execute('''
            SELECT grid, COUNT(*), SUM(outage_count) 
            FROM sector_outage_data 
            WHERE date = ? 
            GROUP BY grid 
            ORDER BY grid
        ''', (date,))
        results = cursor.fetchall()
        
        print(f'\n{date}:')
        if results:
            total_outages = 0
            for grid, count, total in results:
                print(f'  {grid}: {count}条记录, 总退服{total}次')
                total_outages += total if total else 0
            print(f'  总计: {total_outages}次')
        else:
            print('  无扇区数据')
    
    print('\n=== 检查日度数据 ===')
    cursor.execute('''
        SELECT date, district, fault_count 
        FROM daily_data 
        WHERE date BETWEEN '2025-08-01' AND '2025-08-05'
        ORDER BY date, district
    ''')
    daily_results = cursor.fetchall()
    
    current_date = None
    date_total = 0
    for date, district, count in daily_results:
        if current_date != date:
            if current_date:
                print(f'  总计: {date_total}次')
            current_date = date
            date_total = 0
            print(f'\n{date}:')
        print(f'  {district}: {count}次')
        date_total += count
    
    if current_date:
        print(f'  总计: {date_total}次')
    
    conn.close()

if __name__ == "__main__":
    check_sector_data()
