{% extends "base.html" %}

{% block title %}处理结果 - 菏泽数据提取工具{% endblock %}

{% block styles %}
<style>
/* 快速推送操作栏样式 */
.quick-push-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-push-card .card-header {
    border: none;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.quick-push-section {
    transition: all 0.3s ease;
}

.quick-push-section:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-lg:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg small {
    font-size: 0.85rem;
    opacity: 0.9;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.dropdown-header {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.alert-sm {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
}

.status-indicator {
    font-size: 0.85rem;
    line-height: 1.4;
}

.border-end {
    border-right: 2px solid #e9ecef !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 2px solid #e9ecef !important;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }

    .quick-push-section .ps-3 {
        padding-left: 0 !important;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 10px 20px;
    }
}

/* 原有样式保持不变 */
.table-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.table-container table {
    margin-bottom: 0;
}

.table-container .sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}

.correction-info {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.file-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary">
                <i class="bi bi-check-circle"></i>
                {% if is_historical %}
                历史数据报表
                {% else %}
                数据处理完成
                {% endif %}
            </h2>
            <div class="d-flex flex-wrap gap-2 align-items-center">
                {% if is_historical %}
                <a href="{{ url_for('history') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回历史
                </a>
                {% else %}
                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回上传
                </a>
                {% endif %}

                <!-- 邮件发送按钮 -->
                <div class="btn-group" role="group">
                    <button class="btn btn-primary" onclick="showCustomEmailModal()">
                        <i class="bi bi-envelope"></i> 发送邮件报告
                    </button>
                    <button class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">📧 邮件选项</h6></li>
                        <li><a class="dropdown-item" href="#" onclick="showCustomEmailModal()">
                            <i class="bi bi-people text-primary"></i> 自定义收件人
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="sendLibeiEmail()">
                            <i class="bi bi-envelope-heart text-warning"></i> 李贝专用邮件
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showLibeiConfigModal()">
                            <i class="bi bi-gear text-secondary"></i> 李贝邮箱配置
                        </a></li>
                    </ul>
                </div>



                <!-- 钉钉发送控制区域 -->
                <div class="card mt-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-dots"></i> 钉钉发送控制 <span class="badge bg-secondary">传统方式</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2 align-items-center border rounded p-2 bg-light">
                    <!-- 钉钉群组选择 -->
                    <div class="d-flex align-items-center">
                        <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">
                            <i class="bi bi-people"></i> 群组:
                        </label>
                        <select class="form-select form-select-sm" id="dingtalkGroupSelect" style="width: 140px;" onchange="switchDingtalkGroup()">
                            <option value="">加载中...</option>
                        </select>
                    </div>

                    <!-- API类型选择 -->
                    <div class="d-flex align-items-center">
                        <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">API:</label>
                        <select class="form-select form-select-sm" id="globalApiType" style="width: 100px;">
                            <option value="official" selected>官方API</option>
                        </select>
                    </div>

                    <!-- 趋势图选项 -->
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeTrends" checked onchange="updateCombineAllChartsOption()">
                        <label class="form-check-label" for="includeTrends" style="font-size: 0.875rem;">
                            包含趋势图
                        </label>
                    </div>

                    <!-- 三合一选项 -->
                    <div class="form-check" id="combineAllChartsContainer" style="display: block;">
                        <input class="form-check-input" type="checkbox" id="combineAllCharts">
                        <label class="form-check-label" for="combineAllCharts" style="font-size: 0.875rem; color: #28a745;">
                            <i class="bi bi-layers"></i> 三合一（综合报表+扇区数据+趋势图合并成一张图）
                        </label>
                    </div>

                    <!-- 时间范围 -->
                    <div class="d-flex align-items-center" id="dingtalkTrendTimeRangeContainer">
                        <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">范围:</label>
                        <select class="form-select form-select-sm" id="dingtalkTrendTimeRange" style="width: 120px;">
                            <option value="all">全部数据</option>
                            <option value="current_month" selected>当月数据</option>
                        </select>
                    </div>

                    <!-- 报告格式 -->
                    <div class="d-flex align-items-center">
                        <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">格式:</label>
                        <select class="form-select form-select-sm" id="reportFormat" style="width: 100px;">
                            <option value="images">图片格式</option>
                            <option value="pdf">PDF报告</option>
                            <option value="both">图片+PDF</option>
                        </select>
                    </div>

                    <!-- 上传方式 -->
                    <div class="d-flex align-items-center">
                        <label class="form-label mb-0 me-2 text-nowrap" style="font-size: 0.875rem;">上传:</label>
                        <select class="form-select form-select-sm" id="imageUploadMethod" style="width: 100px;" onchange="updateUploadMethodHint()">
                            <option value="dingtalk_native" selected>钉钉原生</option>
                        </select>
                        <small class="text-muted ms-1" id="uploadMethodHint" style="font-size: 0.75rem;"></small>
                    </div>
                </div>

                <!-- 钉钉发送按钮组 -->
                <div class="btn-group" role="group">
                    <button class="btn btn-success" onclick="sendQuickDingtalk()">
                        <i class="bi bi-chat-dots"></i> 发送钉钉消息
                    </button>
                    <button class="btn btn-outline-success dropdown-toggle dropdown-toggle-split" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="visually-hidden">Toggle Dropdown</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">📱 发送选项</h6></li>
                        <li><a class="dropdown-item" href="#" onclick="sendToDingtalk('multiple_images')">
                            <i class="bi bi-images text-success"></i> 多图片发送
                            <small class="text-muted d-block">发送综合报表+扇区数据+趋势图</small>
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="sendToDingtalk('single_images')">
                            <i class="bi bi-image text-primary"></i> 单图片发送
                            <small class="text-muted d-block">逐个发送每张图片</small>
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="sendToDingtalk('text_only')">
                            <i class="bi bi-chat-text text-info"></i> 纯文本发送
                            <small class="text-muted d-block">只发送数据摘要文本</small>
                        </a></li>
                    </ul>
                </div>

                <!-- 其他功能按钮 -->
                <button class="btn btn-outline-info" onclick="showDebugLog()">
                    <i class="bi bi-bug"></i> 调试日志
                </button>

                <button class="btn btn-outline-success" onclick="downloadResults()">
                    <i class="bi bi-download"></i> 下载结果
                </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 日期信息和关联关系 -->
        {% if date_info %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-calendar-check"></i>
                            数据日期信息与关联关系
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% if date_info.daily %}
                            <div class="col-md-6">
                                <div class="border-end pe-3">
                                    <h6 class="text-info">
                                        <i class="bi bi-calendar-day"></i>
                                        日度数据
                                    </h6>
                                    {% if date_info.daily.date %}
                                    <p class="mb-1"><strong>数据日期：</strong>{{ date_info.daily.date }}</p>
                                    {% if date_info.daily.is_cumulative %}
                                    <p class="mb-1"><strong>数据说明：</strong>{{ date_info.daily.start_date }} 至 {{ date_info.daily.date }} 的累计退服故障数据 <span class="badge bg-info">累计数据</span></p>
                                    <p class="mb-1"><small class="text-muted">时间跨度：{{ date_info.daily.actual_period_days }}天</small></p>
                                    {% else %}
                                    <p class="mb-1"><strong>数据说明：</strong>{{ date_info.daily.date }} 当天的退服故障数据 <span class="badge bg-success">单日数据</span></p>
                                    {% endif %}
                                    {% else %}
                                    <p class="text-muted">未能识别日期信息</p>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                            {% if date_info.monthly %}
                            <div class="col-md-6">
                                <div class="ps-3">
                                    <h6 class="text-warning">
                                        <i class="bi bi-calendar-month"></i>
                                        月度数据
                                    </h6>
                                    {% if date_info.monthly.start_date and date_info.monthly.end_date %}
                                    <p class="mb-1"><strong>累计期间：</strong>{{ date_info.monthly.start_date }} 至 {{ date_info.monthly.end_date }}</p>
                                    <p class="mb-1"><strong>数据说明：</strong>从月初到 {{ date_info.monthly.end_date }} 的累计退服故障数据</p>
                                    {% else %}
                                    <p class="text-muted">未能识别日期信息</p>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- 关联关系说明 -->
                        {% if date_info.daily and date_info.monthly and date_info.daily.date == date_info.monthly.end_date %}
                        <hr>
                        <div class="alert alert-info mb-0">
                            <i class="bi bi-link-45deg"></i>
                            <strong>数据关联关系：</strong>
                            {% if date_info.daily.is_cumulative %}
                            日度数据（{{ date_info.daily.start_date }} 至 {{ date_info.daily.date }}）与月度数据（{{ date_info.monthly.start_date }} 至 {{ date_info.monthly.end_date }}）
                            时间范围一致，均为累计统计数据。综合报表中的"当日退服数"已从扇区详单数据中精确计算。
                            {% else %}
                            日度数据（{{ date_info.daily.date }}）与月度数据（{{ date_info.monthly.start_date }} 至 {{ date_info.monthly.end_date }}）
                            时间一致，月度数据包含了该日度数据的累计值。
                            {% endif %}
                        </div>
                        {% elif date_info.daily and date_info.monthly %}
                        <hr>
                        <div class="alert alert-warning mb-0">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>注意：</strong>
                            日度数据日期（{{ date_info.daily.date }}）与月度数据截止日期（{{ date_info.monthly.end_date }}）不一致，
                            请确认数据的时间关系。
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 校正信息 -->
        {% if correction_info %}
        <div class="correction-info">
            <h5 class="text-warning">
                <i class="bi bi-exclamation-triangle"></i>
                数据校正信息
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>扇区故障次数总计：</strong>{{ correction_info.total_standard }}</p>
                    <p><strong>各区县数据总和：</strong>{{ correction_info.total_original }}</p>
                    <p><strong>差值：</strong>{{ correction_info.difference }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>鲁西新区原始数据：</strong>{{ correction_info.luxi_original }}</p>
                    <p><strong>鲁西新区校正后：</strong>{{ correction_info.luxi_corrected }}</p>
                    <p class="text-success"><strong>✓ 已自动校正数据一致性</strong></p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 数据完整性信息 -->
        {% if completion_info and completion_info.missing_districts %}
        <div class="alert alert-info">
            <h6 class="alert-heading">
                <i class="bi bi-info-circle"></i>
                数据完整性补充
            </h6>
            <p class="mb-2">
                <strong>标准网格总数：</strong>{{ completion_info.total_districts }} 个<br>
                <strong>实际找到：</strong>{{ completion_info.found_districts }} 个<br>
                <strong>补充为0：</strong>{{ completion_info.added_zero_districts }} 个
            </p>
            {% if completion_info.missing_districts %}
            <p class="mb-0">
                <strong>补充的网格：</strong>
                {% for district in completion_info.missing_districts %}
                <span class="badge bg-secondary me-1">{{ district }}</span>
                {% endfor %}
                <small class="text-muted">（这些网格当天无故障，已设为0）</small>
            </p>
            {% endif %}
        </div>
        {% endif %}

        <!-- 日度数据结果 -->
        {% if results.daily is defined and not results.daily.empty %}
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-day"></i>
                    日度数据结果
                    {% if date_info.daily and date_info.daily.date %}
                    <small class="ms-2">({{ date_info.daily.date }})</small>
                    {% endif %}
                    <span class="badge bg-light text-dark ms-2">{{ results.daily.shape[0] if results.daily is not none and not results.daily.empty else 0 }} 行</span>
                </h5>
            </div>
        {% elif has_daily is defined and not has_daily %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-day"></i>
                    日度数据结果
                    <span class="badge bg-warning text-dark ms-2">数据缺失</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>日度数据缺失</strong><br>
                    该日期没有找到日度数据，可能原因：
                    <ul class="mb-0 mt-2">
                        <li>该日期只上传了月度文件</li>
                        <li>日度数据处理时出现错误</li>
                        <li>数据库中该日期的日度数据被删除</li>
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        {% if results.daily is defined and not results.daily.empty %}
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                {% for col in results.daily.columns %}
                                <th>{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% if results.daily is not none and not results.daily.empty %}
                            {% for _, row in results.daily.iterrows() %}
                            <tr {% if row['网格'] == '总计' %}class="table-warning fw-bold"{% elif completion_info and row['网格'] in completion_info.missing_districts %}class="table-light text-muted"{% endif %}>
                                {% for col in results.daily.columns %}
                                <td>
                                    {{ row[col] }}
                                    {% if completion_info and row['网格'] in completion_info.missing_districts and col == '总退服次数' %}
                                    <small class="text-muted">
                                        <i class="bi bi-plus-circle" title="补充数据"></i>
                                    </small>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 月度数据结果 -->
        {% if results.monthly is defined and not results.monthly.empty %}
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-month"></i>
                    月度数据结果
                    {% if date_info.monthly and date_info.monthly.start_date and date_info.monthly.end_date %}
                    <small class="ms-2">({{ date_info.monthly.start_date }} 至 {{ date_info.monthly.end_date }})</small>
                    {% endif %}
                    <span class="badge bg-dark text-light ms-2">{{ results.monthly.shape[0] if results.monthly is not none and not results.monthly.empty else 0 }} 行</span>
                    {% if correction_info %}
                    <span class="badge bg-success ms-1">已校正</span>
                    {% endif %}
                </h5>
            </div>
        {% elif has_monthly is defined and not has_monthly %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-month"></i>
                    月度数据结果
                    <span class="badge bg-warning text-dark ms-2">数据缺失</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>月度数据缺失</strong><br>
                    该日期没有找到月度数据，可能原因：
                    <ul class="mb-0 mt-2">
                        <li>该日期只上传了日度文件</li>
                        <li>月度数据处理时出现错误</li>
                        <li>数据库中该日期的月度数据被删除</li>
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        {% if results.monthly is defined and not results.monthly.empty %}
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                {% for col in results.monthly.columns %}
                                <th>{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% if results.monthly is not none and not results.monthly.empty %}
                            {% for _, row in results.monthly.iterrows() %}
                            <tr {% if row['地市_网格'] == '总计' %}class="table-warning fw-bold"{% elif correction_info and '鲁西新区' in row['地市_网格'] %}class="table-success"{% endif %}>
                                {% for col in results.monthly.columns %}
                                <td>
                                    {{ row[col] }}
                                    {% if correction_info and col == '月累计退服总数' and '鲁西新区' in row['地市_网格'] %}
                                    <small class="text-success">
                                        <i class="bi bi-arrow-up"></i>
                                        (+{{ correction_info.difference }})
                                    </small>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 扇区数据详情表 -->
        {% set has_sector_data = results.monthly is defined and not results.monthly.empty and '4G扇区总数' in results.monthly.columns %}
        {% set has_raw_sector_data = sector_data is defined and sector_data is not none %}
        {% if has_sector_data or has_raw_sector_data %}
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-diagram-3"></i>
                    扇区数据详情表
                    {% if date_info.monthly and date_info.monthly.start_date and date_info.monthly.end_date %}
                    <small class="ms-2">({{ date_info.monthly.start_date }} 至 {{ date_info.monthly.end_date }})</small>
                    {% endif %}
                    <span class="badge bg-light text-dark ms-2">{{ results.monthly.shape[0] if results.monthly is not none and not results.monthly.empty else 0 }} 行</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th class="text-center">地市</th>
                                <th class="text-center">地市_网格</th>
                                <th class="text-center">4G扇区总数</th>
                                <th class="text-center">5G扇区总数</th>
                                <th class="text-center">网格总扇区数</th>
                                <th class="text-center">月累计退服总数</th>
                                <th class="text-center">平均扇区退服次数</th>
                                <th class="text-center">平均扇区退服次数目标值</th>
                                <th class="text-center">达标率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if has_sector_data and results.monthly is not none and not results.monthly.empty %}
                            {% for _, row in results.monthly.iterrows() %}
                            <tr {% if row['地市_网格'] == '总计' %}class="table-warning fw-bold"{% elif correction_info and '鲁西新区' in row['地市_网格'] %}class="table-success"{% endif %}>
                                <td class="text-center">{{ row['地市'] }}</td>
                                <td class="text-center">{{ row['地市_网格'] }}</td>
                                <td class="text-center">{{ row.get('4G扇区总数', '') }}</td>
                                <td class="text-center">{{ row.get('5G扇区总数', '') }}</td>
                                <td class="text-center">{{ row.get('网格总扇区数', '') }}</td>
                                <td class="text-center">
                                    {{ row['月累计退服总数'] }}
                                    {% if correction_info and '鲁西新区' in row['地市_网格'] %}
                                    <small class="text-success">
                                        <i class="bi bi-arrow-up"></i>
                                        (+{{ correction_info.difference }})
                                    </small>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ row.get('平均扇区退服次数', '') }}</td>
                                <td class="text-center">{{ row.get('平均扇区退服次数目标值', '') }}</td>
                                <td class="text-center">{{ row.get('达标率', '') }}</td>
                            </tr>
                            {% endfor %}
                            {% elif has_raw_sector_data and sector_data is not none %}
                            {% for _, row in sector_data.iterrows() %}
                            <tr>
                                <td class="text-center">{{ row.get('地市', '菏泽') }}</td>
                                <td class="text-center">{{ row['地市_网格'] }}</td>
                                <td class="text-center">{{ row.get('4G扇区总数', '') }}</td>
                                <td class="text-center">{{ row.get('5G扇区总数', '') }}</td>
                                <td class="text-center">{{ row.get('网格总扇区数', '') }}</td>
                                <td class="text-center">{{ row.get('月累计退服总数', '') }}</td>
                                <td class="text-center">{{ row.get('平均扇区退服次数', '') }}</td>
                                <td class="text-center">{{ row.get('平均扇区退服次数目标值', '') }}</td>
                                <td class="text-center">
                                    {% set rate_value = row.get('达标率', '0.0%') %}
                                    {% set status = row.get('达标状态', '未知') %}
                                    {% set color_class = row.get('状态颜色', 'secondary') %}
                                    {% set time_baseline = row.get('时序基准', '0.0%') %}

                                    <span class="text-{{ color_class }}" title="时序基准: {{ time_baseline }}">
                                        {{ rate_value }}
                                        {% if status == '良好' %}
                                        <i class="bi bi-check-circle-fill text-success ms-1"></i>
                                        {% elif status == '超标' %}
                                        <i class="bi bi-exclamation-triangle-fill text-danger ms-1"></i>
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="row text-center">
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            <strong>扇区说明：</strong>4G和5G扇区总数统计
                        </small>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="bi bi-calculator"></i>
                            <strong>平均次数：</strong>月累计退服数/网格总扇区数
                        </small>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="bi bi-target"></i>
                            <strong>目标值：</strong>根据月份和网格的目标常量
                        </small>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            <i class="bi bi-percent"></i>
                            <strong>达标率：</strong>实际值/目标值×100%
                            <br><strong>时序基准：</strong>当前天数/总天数×100%
                            <br><span class="text-success">≤时序基准良好</span>
                            <span class="text-danger">>时序基准超标</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 综合报表 -->
        {% if report_data is defined and not report_data.empty %}
        <div class="card mb-4">
        {% elif (has_daily is defined and has_daily) and (has_monthly is defined and has_monthly) %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    综合分析报表
                    <span class="badge bg-warning text-dark ms-2">生成失败</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger mb-0">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>综合报表生成失败</strong><br>
                    虽然日度和月度数据都存在，但综合报表生成过程中出现错误。请检查数据格式或联系技术支持。
                </div>
            </div>
        </div>
        {% elif (has_daily is defined and not has_daily) or (has_monthly is defined and not has_monthly) %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    综合分析报表
                    <span class="badge bg-warning text-dark ms-2">无法生成</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-0">
                    <i class="bi bi-info-circle"></i>
                    <strong>无法生成综合报表</strong><br>
                    综合报表需要同时具备日度数据和月度数据才能生成。当前缺少：
                    {% if has_daily is defined and not has_daily %}
                    <span class="badge bg-warning ms-1">日度数据</span>
                    {% endif %}
                    {% if has_monthly is defined and not has_monthly %}
                    <span class="badge bg-warning ms-1">月度数据</span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        {% if report_data is defined and not report_data.empty %}
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {{ report_info.title if report_info else '综合分析报表' }}
                    <span class="badge bg-light text-dark ms-2">{{ report_data.shape[0] if report_data is not none and not report_data.empty else 0 }} 行</span>
                </h5>
            </div>
            <div class="card-body p-0">
                {% if report_info %}
                <div class="alert alert-info mb-0 rounded-0">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>分析日期：</strong>{{ report_info.date }}
                        </div>
                        <div class="col-md-3">
                            <strong>当月第：</strong>{{ report_info.day }} 天
                        </div>
                        <div class="col-md-3">
                            <strong>当月总天数：</strong>{{ report_info.days_in_month }} 天
                        </div>
                        <div class="col-md-3">
                            <strong>月度进度：</strong>{{ "%.1f"|format(report_info.day / report_info.days_in_month * 100) }}%
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="table-container">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-dark sticky-top">
                            <tr>
                                {% for col in report_data.columns %}
                                <th class="text-center">{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for _, row in report_data.iterrows() %}
                            <tr {% if row['地市_网格'] == '总计' %}class="table-warning fw-bold"{% endif %}>
                                {% for col in report_data.columns %}
                                <td class="text-center">
                                    {% if col == '差值' %}
                                        {% if row[col] > 0 %}
                                        <span class="text-success">+{{ row[col] }}</span>
                                        {% elif row[col] < 0 %}
                                        <span class="text-danger">{{ row[col] }}</span>
                                        {% else %}
                                        <span class="text-muted">{{ row[col] }}</span>
                                        {% endif %}
                                    {% elif col == '时序' %}
                                        {% set time_val = row[col]|replace('%', '')|float %}
                                        {% if time_val > 100 %}
                                        <span class="text-danger">{{ row[col] }}</span>
                                        {% elif time_val > 90 %}
                                        <span class="text-warning">{{ row[col] }}</span>
                                        {% else %}
                                        <span class="text-success">{{ row[col] }}</span>
                                        {% endif %}
                                    {% else %}
                                    {{ row[col] }}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="row text-center">
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            <strong>差值说明：</strong>正值表示优于目标，负值表示超出目标
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="bi bi-clock"></i>
                            <strong>时序说明：</strong>当前进度相对于预期进度的百分比
                        </small>
                    </div>
                    <div class="col-md-4">
                        <small class="text-muted">
                            <i class="bi bi-target"></i>
                            <strong>目标说明：</strong>基于年度目标按月分解计算
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 数据总结 -->
        {% if summary_text %}
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-data"></i>
                    数据分析总结
                </h5>
            </div>
            <div class="card-body">
                <div class="summary-content">
                    {{ summary_text | replace('\n', '<br>') | safe }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 特殊格式数据显示区域 -->
        {% if libei_data is defined and libei_data is not none and not libei_data.empty %}
        <!-- 使用说明 -->
        <div class="alert alert-info mb-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="alert-heading mb-2">
                        <i class="bi bi-info-circle"></i> 菏泽退服故障数据使用说明
                    </h6>
                    <p class="mb-0 small">
                        <strong>1. 下载文件：</strong>点击"下载Excel文件"按钮保存到本地
                        <strong>2. 发送邮件：</strong>选择"自定义邮箱"可上传刚下载的文件作为附件
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-warning btn-sm" onclick="downloadLibeiData()">
                        <i class="bi bi-download"></i> 立即下载
                    </button>
                </div>
            </div>
        </div>

        <div class="card mb-4" style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: none; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #28a745, #20c997);">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-white">
                        <i class="bi bi-table"></i>
                        菏泽退服故障数据表
                        <span class="badge bg-light text-dark ms-2">{{ libei_data.shape[0] if libei_data is not none and not libei_data.empty else 0 }} 行</span>
                    </h5>
                    <div>
                        <button class="btn btn-warning btn-sm me-2" onclick="downloadLibeiData()">
                            <i class="bi bi-download"></i> 下载Excel文件
                        </button>
                        <div class="btn-group">
                            <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-envelope-heart"></i> 发送邮件
                            </button>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">选择收件人</h6></li>
                                <li><a class="dropdown-item" href="#" onclick="showLibeiEmailModal()">
                                    <i class="bi bi-gear"></i> 自定义邮箱（可上传附件）
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="sendLibeiEmailQuick('<EMAIL>')">
                                    <i class="bi bi-envelope"></i> 默认邮箱（仅文本）
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0" style="background: linear-gradient(135deg, #f1f3f4, #e8eaed); border-radius: 0 0 0.375rem 0.375rem;">
                <div class="table-container" style="background: rgba(255, 255, 255, 0.9); margin: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);">
                    <table class="table table-striped table-hover mb-0" style="background: transparent;">
                        <thead class="table-success sticky-top" style="background: linear-gradient(135deg, #28a745, #20c997) !important;">
                            <tr>
                                {% for col in libei_data.columns %}
                                <th class="text-center text-white" style="border: none; padding: 12px 8px; font-weight: 600;">{{ col }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for _, row in libei_data.iterrows() %}
                            <tr {% if row['地市_网格'] == '总计' %}style="background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important; font-weight: bold; border-top: 2px solid #ffc107;"{% else %}style="background: rgba(255, 255, 255, 0.8); transition: all 0.2s ease;"{% endif %}
                                onmouseover="{% if row['地市_网格'] != '总计' %}this.style.background='rgba(40, 167, 69, 0.1)'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)';{% endif %}"
                                onmouseout="{% if row['地市_网格'] != '总计' %}this.style.background='rgba(255, 255, 255, 0.8)'; this.style.transform='translateY(0)'; this.style.boxShadow='none';{% endif %}">
                                {% for col in libei_data.columns %}
                                <td class="text-center" style="padding: 10px 8px; border: none; border-bottom: 1px solid rgba(0,0,0,0.05);">
                                    {% if col == '差值' %}
                                        {% set diff_val = row[col] %}
                                        {% if diff_val > 0 %}
                                        <span class="fw-bold" style="color: #28a745; background: rgba(40, 167, 69, 0.1); padding: 2px 6px; border-radius: 4px;">{{ diff_val }}</span>
                                        {% elif diff_val < 0 %}
                                        <span class="fw-bold" style="color: #dc3545; background: rgba(220, 53, 69, 0.1); padding: 2px 6px; border-radius: 4px;">{{ diff_val }}</span>
                                        {% else %}
                                        <span style="color: #6c757d;">{{ diff_val }}</span>
                                        {% endif %}
                                    {% elif col == '时序' %}
                                        {% set time_val = row[col]|replace('%', '')|float %}
                                        {% if time_val > 100 %}
                                        <span style="color: #dc3545; background: rgba(220, 53, 69, 0.1); padding: 2px 6px; border-radius: 4px; font-weight: 600;">{{ row[col] }}</span>
                                        {% elif time_val > 90 %}
                                        <span style="color: #fd7e14; background: rgba(253, 126, 20, 0.1); padding: 2px 6px; border-radius: 4px; font-weight: 600;">{{ row[col] }}</span>
                                        {% else %}
                                        <span style="color: #28a745; background: rgba(40, 167, 69, 0.1); padding: 2px 6px; border-radius: 4px; font-weight: 600;">{{ row[col] }}</span>
                                        {% endif %}
                                    {% elif col == '当月日均故障数量' %}
                                        <span class="fw-bold" style="color: #0d6efd; background: rgba(13, 110, 253, 0.1); padding: 2px 6px; border-radius: 4px;">{{ row[col] }}</span>
                                    {% elif col == '地市_网格' %}
                                        <span class="fw-bold" style="color: #495057;">{{ row[col] }}</span>
                                    {% else %}
                                    <span style="color: #495057;">{{ row[col] }}</span>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
        {% endif %}

        <!-- 调试日志输出框 -->
        <div class="card mb-4" id="debugLogCard" style="display: none;">
            <div class="card-header bg-dark text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-terminal"></i> 调试日志
                    </h6>
                    <div>
                        <button class="btn btn-outline-light btn-sm" onclick="clearDebugLog()">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="hideDebugLog()">
                            <i class="bi bi-x"></i> 隐藏
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="debugLogContent" class="p-3" style="background-color: #1e1e1e; color: #d4d4d4; font-family: 'Courier New', monospace; font-size: 12px; max-height: 400px; overflow-y: auto;">
                    <!-- 日志内容将在这里显示 -->
                </div>
            </div>
        </div>


    </div>
</div>


<!-- 李贝邮件发送模态框 -->
<div class="modal fade" id="libeiEmailModal" tabindex="-1" aria-labelledby="libeiEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="libeiEmailModalLabel">
                    <i class="bi bi-envelope"></i> 菏泽退服故障邮件发送
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="libeiEmailForm">
                    <!-- 收件人 -->
                    <div class="mb-3">
                        <label for="libeiRecipients" class="form-label">
                            <i class="bi bi-envelope"></i> 收件人 <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="libeiRecipients" name="libeiRecipients" rows="2"
                                  placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>" required><EMAIL></textarea>
                        <div class="form-text">每行输入一个邮箱地址</div>
                    </div>

                    <!-- 抄送 -->
                    <div class="mb-3">
                        <label for="libeiCc" class="form-label">
                            <i class="bi bi-envelope-plus"></i> 抄送（可选）
                        </label>
                        <textarea class="form-control" id="libeiCc" name="libeiCc" rows="2"
                                  placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="form-text">抄送人会收到邮件副本</div>
                    </div>

                    <!-- 附件选项 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeLibeiAttachment">
                            <label class="form-check-label" for="includeLibeiAttachment">
                                <i class="bi bi-paperclip"></i> 包含Excel附件
                            </label>
                        </div>
                        <div class="form-text text-info">
                            <i class="bi bi-info-circle"></i>
                            请先下载特殊格式Excel文件，然后在下方上传作为附件
                        </div>
                    </div>

                    <!-- 附件上传 -->
                    <div class="mb-3" id="libeiAttachmentUpload" style="display: none;">
                        <label for="libeiAttachmentFile" class="form-label">
                            <i class="bi bi-upload"></i> 上传Excel附件
                        </label>
                        <input type="file" class="form-control" id="libeiAttachmentFile"
                               accept=".xlsx,.xls" onchange="uploadLibeiAttachment()">
                        <div class="form-text">
                            支持 .xlsx 和 .xls 格式，最大10MB
                        </div>
                        <div id="libeiAttachmentStatus" class="mt-2"></div>
                    </div>

                    <!-- 邮件预览 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-eye"></i> 邮件内容预览
                        </label>
                        <div class="border rounded p-3 bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>主题：</strong>
                                    <div class="text-muted small">菏泽移网退服故障报表</div>
                                </div>
                                <div class="col-md-6">
                                    <strong>附件：</strong>
                                    <div class="text-muted small" id="libeiAttachmentPreview">
                                        菏泽退服故障综合报表_YYYYMMDD.xlsx
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <strong>数据说明：</strong>
                                <div class="text-muted small">
                                    • 当日退服数替换为当月日均故障数量<br>
                                    • 差值 = 当日目标 - 当月日均故障数量<br>
                                    • 包含完整的扇区数据和分析
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收件人预览 -->
                    <div class="mb-3">
                        <label class="form-label">收件人预览</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>收件人：</strong>
                                    <div id="libeiRecipientsPreview" class="text-muted small"><EMAIL></div>
                                </div>
                                <div class="col-md-6">
                                    <strong>抄送：</strong>
                                    <div id="libeiCcPreview" class="text-muted small">未设置</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 取消
                </button>
                <button type="button" class="btn btn-outline-success" onclick="saveLibeiEmailConfig()">
                    <i class="bi bi-floppy"></i> 保存配置
                </button>
                <button type="button" class="btn btn-success" onclick="sendLibeiEmailFromModal()">
                    <i class="bi bi-send"></i> 发送邮件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 李贝邮箱配置模态框（保留原有的，用于其他地方） -->
<div class="modal fade" id="libeiConfigModal" tabindex="-1" aria-labelledby="libeiConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="libeiConfigModalLabel">
                    <i class="bi bi-envelope-heart text-success"></i> 李贝邮箱配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="libeiConfigForm">
                    <div class="mb-3">
                        <label for="libeiEmail" class="form-label">
                            <i class="bi bi-envelope"></i> 李贝邮箱地址
                        </label>
                        <input type="email" class="form-control" id="libeiEmail"
                               placeholder="请输入李贝的邮箱地址" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            特殊格式邮件将发送到此邮箱地址
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>李贝邮件特点：</strong>
                        <ul class="mb-0 mt-2">
                            <li>将"当日退服数"替换为"当月日均故障数量"</li>
                            <li>差值 = 当日目标 - 当月日均故障数量</li>
                            <li>当月日均故障数量 = 月累计 ÷ 当前天数</li>
                            <li>其他内容（扇区数据等）保持不变</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-success" onclick="saveLibeiConfig()">
                    <i class="bi bi-check-circle"></i> 保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 邮件组合管理模态框 -->
<div class="modal fade" id="emailCombinationManagerModal" tabindex="-1" aria-labelledby="emailCombinationManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="emailCombinationManagerModalLabel">
                    <i class="bi bi-people-fill"></i> 邮件组合管理
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 添加新组合按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">当前邮件组合</h6>
                    <button type="button" class="btn btn-success btn-sm" onclick="showAddCombinationForm()">
                        <i class="bi bi-plus-circle"></i> 添加新组合
                    </button>
                </div>

                <!-- 组合列表 -->
                <div id="emailCombinationsList">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载邮件组合...</div>
                    </div>
                </div>

                <!-- 添加/编辑组合表单 -->
                <div id="combinationForm" style="display: none;">
                    <hr>
                    <h6 id="combinationFormTitle">添加新邮件组合</h6>
                    <form id="emailCombinationForm">
                        <input type="hidden" id="combinationId" value="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="combinationName" class="form-label">组合名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="combinationName" placeholder="例如：日常报告组" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="combinationDescription" class="form-label">描述</label>
                                    <input type="text" class="form-control" id="combinationDescription" placeholder="组合用途说明">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="combinationRecipients" class="form-label">收件人 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="combinationRecipients" rows="3"
                                              placeholder="每行一个邮箱地址" required></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="combinationCc" class="form-label">抄送</label>
                                    <textarea class="form-control" id="combinationCc" rows="3"
                                              placeholder="每行一个邮箱地址"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="combinationBcc" class="form-label">密送</label>
                                    <textarea class="form-control" id="combinationBcc" rows="3"
                                              placeholder="每行一个邮箱地址"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="combinationIsDefault">
                                <label class="form-check-label" for="combinationIsDefault">
                                    设为默认组合
                                </label>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存组合
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="hideCombinationForm()">
                                <i class="bi bi-x-circle"></i> 取消
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 李贝数据预览模态框 -->
<div class="modal fade" id="libeiPreviewModal" tabindex="-1" aria-labelledby="libeiPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="libeiPreviewModalLabel">
                    <i class="bi bi-eye text-info"></i> 李贝数据预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="libeiPreviewContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在生成李贝数据预览...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 关闭
                </button>
                <button type="button" class="btn btn-success" onclick="sendLibeiEmailFromPreview()">
                    <i class="bi bi-envelope-heart"></i> 发送李贝邮件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 数据可视化模态框 -->
<div class="modal fade" id="visualizationModal" tabindex="-1" aria-labelledby="visualizationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="visualizationModalLabel">
                    <i class="bi bi-bar-chart"></i> 数据可视化
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 图表类型选择 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label class="form-label">
                            <i class="bi bi-graph-up"></i> 图表类型
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="chartType" id="chartBar" value="bar" checked onchange="updateChartPreview()">
                                    <label class="form-check-label" for="chartBar">
                                        <i class="bi bi-bar-chart text-primary"></i> 柱状图
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="chartType" id="chartLine" value="line" onchange="updateChartPreview()">
                                    <label class="form-check-label" for="chartLine">
                                        <i class="bi bi-graph-up text-success"></i> 折线图
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="chartType" id="chartPie" value="pie" onchange="updateChartPreview()">
                                    <label class="form-check-label" for="chartPie">
                                        <i class="bi bi-pie-chart text-warning"></i> 饼图
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="chartType" id="chartTable" value="table" onchange="updateChartPreview()">
                                    <label class="form-check-label" for="chartTable">
                                        <i class="bi bi-table text-info"></i> 数据表格
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">
                            <i class="bi bi-calendar-event"></i> 数据范围
                        </label>
                        <div class="mb-2">
                            <select class="form-select" id="dataRange" onchange="updateChartPreview()">
                                <option value="today">今日数据</option>
                                <option value="week" selected>最近7天</option>
                                <option value="month">最近30天</option>
                                <option value="custom">自定义范围</option>
                            </select>
                        </div>
                        <div id="customDateRange" style="display: none;">
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="startDate">
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="endDate">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表预览区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-eye"></i> 图表预览
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="chartPreviewContainer" class="text-center" style="min-height: 300px;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div>
                                    <i class="bi bi-graph-up display-1 text-muted"></i>
                                    <p class="text-muted mt-2">点击"生成预览"查看图表效果</p>
                                    <button class="btn btn-outline-primary" onclick="generateChartPreview()">
                                        <i class="bi bi-arrow-clockwise"></i> 生成预览
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发送选项 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-send"></i> 发送选项
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="includeTitle" checked>
                                    <label class="form-check-label" for="includeTitle">
                                        包含标题
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="includeDataSummary" checked>
                                    <label class="form-check-label" for="includeDataSummary">
                                        包含数据摘要
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="includeTimestamp" checked>
                                    <label class="form-check-label" for="includeTimestamp">
                                        包含时间戳
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-palette"></i> 样式设置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <label class="form-label">图表主题</label>
                                    <select class="form-select form-select-sm" id="chartTheme">
                                        <option value="default">默认主题</option>
                                        <option value="business">商务主题</option>
                                        <option value="colorful">彩色主题</option>
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">图片质量</label>
                                    <select class="form-select form-select-sm" id="imageQuality">
                                        <option value="standard">标准质量</option>
                                        <option value="high" selected>高质量</option>
                                        <option value="ultra">超高质量</option>
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label">发送方式</label>
                                    <select class="form-select form-select-sm" id="apiType">
                                        <option value="webhook">Webhook方式</option>
                                        <option value="official">官方API方式</option>
                                    </select>
                                    <small class="text-muted">选择钉钉发送方式，内容完全一致</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载指示器 -->
                <div class="text-center mt-3" id="visualizationLoadingIndicator" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">生成中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在生成图表并发送到钉钉...</p>
                </div>

                <!-- 结果显示 -->
                <div id="visualizationResultContainer" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 取消
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="generateChartPreview()">
                    <i class="bi bi-eye"></i> 预览图表
                </button>
                <button type="button" class="btn btn-primary" onclick="sendVisualizationToDingtalk()">
                    <i class="bi bi-send"></i> 发送到钉钉
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 官方API发送模态框 -->
<div class="modal fade" id="officialApiModal" tabindex="-1" aria-labelledby="officialApiModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="officialApiModalLabel">
                    <i class="bi bi-robot"></i> 钉钉官方API发送
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="officialApiForm">
                    <!-- 发送类型选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-list-check"></i> 发送类型
                        </label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="sendType" id="sendExcel" value="excel" checked onchange="updateOfficialApiOptions()">
                                    <label class="form-check-label" for="sendExcel">
                                        <i class="bi bi-file-earmark-excel text-success"></i> Excel报表
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="sendType" id="sendImage" value="image" onchange="updateOfficialApiOptions()">
                                    <label class="form-check-label" for="sendImage">
                                        <i class="bi bi-image text-info"></i> 图片报告
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="sendType" id="sendCard" value="card" onchange="updateOfficialApiOptions()">
                                    <label class="form-check-label" for="sendCard">
                                        <i class="bi bi-card-text text-warning"></i> 交互卡片
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日期选择 -->
                    <div class="mb-3">
                        <label for="officialApiDate" class="form-label">
                            <i class="bi bi-calendar-event"></i> 报表日期
                        </label>
                        <input type="date" class="form-control" id="officialApiDate" required>
                        <div class="form-text">选择要发送的报表日期</div>
                    </div>

                    <!-- 群组选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-people"></i> 目标群组
                        </label>
                        <div id="officialApiGroupSelector" class="border rounded p-3 bg-light">
                            <!-- 动态加载群组选项 -->
                        </div>
                    </div>

                    <!-- Excel选项 -->
                    <div id="excelOptions" class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                            <label class="form-check-label" for="includeCharts">
                                <i class="bi bi-graph-up"></i> 同时发送趋势图
                            </label>
                        </div>
                        <div class="form-text">Excel文件发送后，自动发送趋势图图片</div>
                    </div>

                    <!-- 图片选项 -->
                    <div id="imageOptions" class="mb-3" style="display: none;">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeSummary" checked>
                            <label class="form-check-label" for="includeSummary">
                                <i class="bi bi-chat-text"></i> 包含文字摘要
                            </label>
                        </div>
                        <div class="form-text">图片发送前，先发送文字摘要信息</div>
                    </div>

                    <!-- 卡片选项 -->
                    <div id="cardOptions" class="mb-3" style="display: none;">
                        <div class="mb-2">
                            <label for="cardTitle" class="form-label">卡片标题</label>
                            <input type="text" class="form-control" id="cardTitle" value="菏泽移网退服故障报表">
                        </div>
                        <div class="mb-2">
                            <label for="cardUrl" class="form-label">链接地址</label>
                            <input type="url" class="form-control" id="cardUrl" placeholder="http://your-server.com/report">
                        </div>
                    </div>

                    <!-- 预览信息 -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle"></i> 功能说明
                        </h6>
                        <ul class="mb-0">
                            <li><strong>Excel报表：</strong>直接发送Excel文件到钉钉群，支持手机查看</li>
                            <li><strong>图片报告：</strong>发送趋势图图片，便于快速查看</li>
                            <li><strong>交互卡片：</strong>发送可点击的卡片消息，支持跳转链接</li>
                            <li><strong>官方API：</strong>使用钉钉官方API，功能更强大稳定</li>
                        </ul>
                    </div>
                </form>

                <!-- 加载指示器 -->
                <div class="text-center mt-3" id="officialApiLoadingIndicator" style="display: none;">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">发送中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在通过官方API发送到钉钉...</p>
                </div>

                <!-- 结果显示 -->
                <div id="officialApiResultContainer" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 取消
                </button>
                <button type="button" class="btn btn-success" onclick="sendViaOfficialApi()">
                    <i class="bi bi-send"></i> 发送
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 自定义邮件发送模态框 -->
<div class="modal fade" id="customEmailModal" tabindex="-1" aria-labelledby="customEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customEmailModalLabel">
                    <i class="bi bi-envelope-gear"></i> 自定义邮件发送
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customEmailForm">
                    <!-- 邮箱账户选择 -->
                    <div class="mb-3">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="bi bi-envelope-gear"></i>
                                    发件邮箱账户
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label for="emailAccountSelect" class="form-label">选择发件邮箱</label>
                                        <select class="form-select" id="emailAccountSelect" onchange="updateEmailAccountInfo()">
                                            <option value="">加载中...</option>
                                        </select>
                                        <div class="form-text">选择用于发送邮件的邮箱账户</div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="goToEmailConfig()">
                                            <i class="bi bi-gear"></i> 邮箱配置
                                        </button>
                                    </div>
                                </div>
                                <div id="emailAccountInfo" class="mt-2" style="display: none;">
                                    <div class="alert alert-info mb-0">
                                        <small>
                                            <i class="bi bi-info-circle"></i>
                                            <strong>当前邮箱：</strong><span id="currentEmailInfo">-</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 邮件组合选择 -->
                    <div class="mb-3">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="bi bi-people-fill"></i>
                                    邮件收件人组合
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label for="emailCombination" class="form-label">选择预设组合</label>
                                        <select class="form-select" id="emailCombination" onchange="loadEmailCombination()">
                                            <option value="">请选择邮件组合...</option>
                                        </select>
                                        <div class="form-text">选择预设的收件人组合，或手动填写下方字段</div>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showEmailCombinationManager()">
                                            <i class="bi bi-gear"></i> 管理组合
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- 趋势图选项 -->
                    <div class="mb-3">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="bi bi-graph-up-arrow"></i>
                                    多区县趋势图选项
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="includeTrendCharts" checked onchange="updateTrendChartsOptions()">
                                    <label class="form-check-label" for="includeTrendCharts">
                                        <strong>📊 在邮件中嵌入趋势图（1个总计图 + 10个区县图）</strong>
                                    </label>
                                </div>

                                <div id="trendChartsOptions" style="display: block;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="trendChartTimeRange" class="form-label">趋势图时间范围</label>
                                            <select class="form-select form-select-sm" id="trendChartTimeRange" onchange="updateTrendTimeRangeOptions()">
                                                <option value="all">全部数据趋势图</option>
                                                <option value="current_month" selected>当月数据趋势图</option>
                                                <option value="custom_days">自定义天数</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6" id="customDaysContainer" style="display: none;">
                                            <label for="trendChartDays" class="form-label">自定义天数</label>
                                            <select class="form-select form-select-sm" id="trendChartDays">
                                                <option value="7">最近7天</option>
                                                <option value="15">最近15天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="60">最近60天</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- 时间范围说明 -->
                                    <div class="mt-2">
                                        <div class="alert alert-light py-2 mb-2" id="timeRangeDescription">
                                            <small class="text-muted">
                                                <i class="bi bi-info-circle"></i>
                                                <span id="timeRangeText">显示数据库中所有可用数据的趋势图</span>
                                            </small>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">包含内容</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeTargetLine" checked>
                                                <label class="form-check-label" for="includeTargetLine">
                                                    包含目标线
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 图表模式（隐藏，固定为静态内嵌） -->
                                    <input type="hidden" name="chart_mode" id="chartModeStatic" value="static">

                                    <div class="alert alert-info mt-3 mb-0">
                                        <small>
                                            <i class="bi bi-info-circle"></i>
                                            <strong>说明：</strong>将在邮件中嵌入11个独立的趋势图：1个总计图 + 10个区县图，以2列5行的方式排列显示。
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 扇区数据附件选项 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeSectorAttachment" checked onchange="updateSectorAttachmentPreview()">
                            <label class="form-check-label" for="includeSectorAttachment">
                                <i class="bi bi-database"></i> <strong>自动生成扇区数据附件</strong>
                            </label>
                        </div>
                        <div class="form-text">
                            自动从数据库提取当天的扇区粒度退服故障数据，生成Excel附件
                        </div>
                        <div id="sectorAttachmentPreview" class="mt-2">
                            <div class="alert alert-success d-flex align-items-center">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                <span>将自动生成：扇区粒度退服故障统计清单-天粒度-[日期].xlsx</span>
                            </div>
                        </div>
                    </div>

                    <!-- 重复故障分析附件选项 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeRepeatedOutagesAttachment" onchange="updateRepeatedOutagesAttachmentPreview()">
                            <label class="form-check-label" for="includeRepeatedOutagesAttachment">
                                <i class="bi bi-arrow-repeat text-warning"></i> <strong>自动生成重复故障分析附件</strong>
                            </label>
                        </div>
                        <div class="form-text">
                            自动生成重复故障分析Excel文件作为附件（带颜色编码和详细分布）
                        </div>
                        <div id="repeatedOutagesAttachmentPreview" class="mt-2" style="display: none;">
                            <div class="alert alert-warning d-flex align-items-center">
                                <i class="bi bi-file-earmark-excel me-2"></i>
                                <span>将自动生成：重复故障分析-[日期].xlsx</span>
                            </div>
                        </div>

                        <!-- 重复故障分析参数 -->
                        <div id="repeatedOutagesParams" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-gear"></i> 重复故障分析参数</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="repeatedTargetDate" class="form-label">目标日期</label>
                                            <input type="date" class="form-control" id="repeatedTargetDate">
                                            <small class="form-text text-muted">默认为数据库最新日期</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="repeatedAnalysisMode" class="form-label">分析模式</label>
                                            <select class="form-select" id="repeatedAnalysisMode" onchange="toggleRepeatedAnalysisMode()">
                                                <option value="auto" selected>智能分析（推荐）</option>
                                                <option value="manual">手动指定天数</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="repeatedMinOccurrences" class="form-label">最小重复次数</label>
                                            <select class="form-select" id="repeatedMinOccurrences">
                                                <option value="2">2次及以上</option>
                                                <option value="3">3次及以上</option>
                                                <option value="4">4次及以上</option>
                                                <option value="5" selected>5次及以上</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row" id="repeatedManualDaysRow" style="display: none;">
                                        <div class="col-md-6 mb-3">
                                            <label for="repeatedDaysBack" class="form-label">分析天数</label>
                                            <select class="form-select" id="repeatedDaysBack">
                                                <option value="7">最近7天</option>
                                                <option value="15">最近15天</option>
                                                <option value="30" selected>最近30天</option>
                                                <option value="60">最近60天</option>
                                                <option value="90">最近90天</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="emailAttachment" class="form-label">
                            <i class="bi bi-paperclip"></i> 手动上传附件（可选）
                        </label>
                        <input type="file" class="form-control" id="emailAttachment"
                               accept=".xlsx,.xls,.pdf,.docx,.doc,.txt,.csv"
                               onchange="handleAttachmentUpload(this)">
                        <div class="form-text">
                            支持格式：Excel (.xlsx, .xls)、PDF (.pdf)、Word (.docx, .doc)、文本 (.txt, .csv)
                            <br>最大文件大小：10MB
                        </div>
                        <div id="attachmentPreview" class="mt-2" style="display: none;">
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="bi bi-file-earmark me-2"></i>
                                <span id="attachmentName"></span>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" onclick="removeAttachment()">
                                    <i class="bi bi-x"></i> 移除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 收件人 -->
                    <div class="mb-3">
                        <label for="recipients" class="form-label">收件人 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="recipients" name="recipients" rows="3"
                                  placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>" required></textarea>
                        <div class="form-text">每行输入一个邮箱地址</div>
                    </div>

                    <!-- 抄送 -->
                    <div class="mb-3">
                        <label for="cc" class="form-label">抄送（可选）</label>
                        <textarea class="form-control" id="cc" name="cc" rows="2"
                                  placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="form-text">抄送人会收到邮件副本，其他收件人可以看到</div>
                    </div>

                    <!-- 密送 -->
                    <div class="mb-3">
                        <label for="bcc" class="form-label">密送（可选）</label>
                        <textarea class="form-control" id="bcc" name="bcc" rows="2"
                                  placeholder="每行一个邮箱地址，例如：&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        <div class="form-text">密送人会收到邮件但其他收件人看不到</div>
                    </div>

                    <!-- 预览收件人 -->
                    <div class="mb-3">
                        <label class="form-label">收件人预览</label>
                        <div class="border rounded p-3 bg-light">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>收件人：</strong>
                                    <div id="recipientsPreview" class="text-muted small">未设置</div>
                                </div>
                                <div class="col-md-4">
                                    <strong>抄送：</strong>
                                    <div id="ccPreview" class="text-muted small">未设置</div>
                                </div>
                                <div class="col-md-4">
                                    <strong>密送：</strong>
                                    <div id="bccPreview" class="text-muted small">未设置</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 取消
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="saveEmailConfig()">
                    <i class="bi bi-floppy"></i> 保存配置
                </button>
                <button type="button" class="btn btn-primary" onclick="sendCustomEmail()">
                    <i class="bi bi-send"></i> 发送邮件
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- 优先使用CDN，离线时自动降级到本地文件 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"
        onerror="this.onerror=null;this.src='{{ url_for('static', filename='js/xlsx-local.js') }}';">
</script>
<!-- 离线备用脚本 -->
<script>
    // 检测XLSX是否加载成功，如果失败则加载本地版本
    setTimeout(function() {
        if (typeof XLSX === 'undefined') {
            var script = document.createElement('script');
            script.src = '{{ url_for("static", filename="js/xlsx-local.js") }}';
            document.head.appendChild(script);
        }
    }, 1000);
</script>
<script>
function downloadResults() {
    addDebugLog('📥 开始下载结果文件', 'info');
    try {
        addDebugLog('📊 创建Excel工作簿', 'info');
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        {% if results.daily is defined and results.daily is not none and not results.daily.empty %}
        // 添加日度数据工作表
        const dailyData = [];
        const dailyHeaders = {{ results.daily.columns.tolist() | tojson }};
        dailyData.push(dailyHeaders);

        {% for _, row in results.daily.iterrows() %}
        dailyData.push([
            {% for col in results.daily.columns %}
            "{{ row[col] }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ]);
        {% endfor %}

        const dailyWs = XLSX.utils.aoa_to_sheet(dailyData);
        XLSX.utils.book_append_sheet(wb, dailyWs, "日度数据");
        {% endif %}

        {% if results.monthly is defined and results.monthly is not none and not results.monthly.empty %}
        // 添加月度数据工作表
        const monthlyData = [];
        const monthlyHeaders = {{ results.monthly.columns.tolist() | tojson }};
        monthlyData.push(monthlyHeaders);

        {% for _, row in results.monthly.iterrows() %}
        monthlyData.push([
            {% for col in results.monthly.columns %}
            "{{ row[col] }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ]);
        {% endfor %}

        const monthlyWs = XLSX.utils.aoa_to_sheet(monthlyData);
        XLSX.utils.book_append_sheet(wb, monthlyWs, "月度数据");
        {% endif %}

        {% if report_data is defined and report_data is not none and not report_data.empty %}
        // 添加综合报表工作表
        const reportData = [];
        const reportHeaders = {{ report_data.columns.tolist() | tojson }};
        reportData.push(reportHeaders);

        {% for _, row in report_data.iterrows() %}
        reportData.push([
            {% for col in report_data.columns %}
            "{{ row[col] }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ]);
        {% endfor %}

        const reportWs = XLSX.utils.aoa_to_sheet(reportData);
        XLSX.utils.book_append_sheet(wb, reportWs, "综合报表");
        {% endif %}

        {% if libei_data is defined and libei_data is not none and not libei_data.empty %}
        // 添加特殊格式数据工作表
        const libeiData = [];
        const libeiHeaders = {{ libei_data.columns.tolist() | tojson }};
        libeiData.push(libeiHeaders);

        {% for _, row in libei_data.iterrows() %}
        libeiData.push([
            {% for col in libei_data.columns %}
            "{{ row[col] }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ]);
        {% endfor %}

        const libeiWs = XLSX.utils.aoa_to_sheet(libeiData);
        XLSX.utils.book_append_sheet(wb, libeiWs, "菏泽退服故障数据");
        {% endif %}

        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_');
        const filename = `菏泽数据提取结果_${timestamp}.xlsx`;
        addDebugLog(`📁 生成文件名: ${filename}`, 'info');

        // 下载文件
        XLSX.writeFile(wb, filename);
        addDebugLog('✅ 文件下载成功', 'success');

    } catch (error) {
        addDebugLog(`❌ 下载失败: ${error.message}`, 'error');
        console.error('下载失败:', error);
        alert('下载失败，请检查浏览器是否支持文件下载功能');
    }
}

// 下载特殊格式数据
function downloadLibeiData() {
    try {
        {% if libei_data is defined and libei_data is not none and not libei_data.empty %}
        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 添加数据工作表
        const libeiData = [];
        const libeiHeaders = {{ libei_data.columns.tolist() | tojson }};
        libeiData.push(libeiHeaders);

        {% for _, row in libei_data.iterrows() %}
        libeiData.push([
            {% for col in libei_data.columns %}
            "{{ row[col] }}"{% if not loop.last %},{% endif %}
            {% endfor %}
        ]);
        {% endfor %}

        const libeiWs = XLSX.utils.aoa_to_sheet(libeiData);
        XLSX.utils.book_append_sheet(wb, libeiWs, "菏泽退服故障数据");

        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const filename = `菏泽退服故障综合报表_${timestamp}.xlsx`;

        // 下载文件
        XLSX.writeFile(wb, filename);

        showMessage('菏泽退服故障数据下载成功！', 'success');
        {% else %}
        showMessage('数据不可用，无法下载', 'error');
        {% endif %}

    } catch (error) {
        console.error('李贝数据下载失败:', error);
        showMessage('下载失败，请检查浏览器是否支持文件下载功能', 'error');
    }
}

// 显示李贝邮件模态框
function showLibeiEmailModal() {
    // 加载默认配置
    fetch('/api/libei_email_config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.config;
                document.getElementById('libeiRecipients').value = config.recipients.join('\n');
                document.getElementById('libeiCc').value = config.cc.join('\n');
                updateLibeiEmailPreview();
            }
        })
        .catch(error => {
            console.error('加载李贝邮件配置失败:', error);
        });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('libeiEmailModal'));
    modal.show();
}

// 更新李贝邮件预览
function updateLibeiEmailPreview() {
    const recipients = document.getElementById('libeiRecipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('libeiCc').value.split('\n').filter(e => e.trim());
    const includeAttachment = document.getElementById('includeLibeiAttachment').checked;

    document.getElementById('libeiRecipientsPreview').textContent =
        recipients.length > 0 ? recipients.join(', ') : '未设置';
    document.getElementById('libeiCcPreview').textContent =
        cc.length > 0 ? cc.join(', ') : '未设置';

    // 显示/隐藏附件上传区域
    const attachmentUpload = document.getElementById('libeiAttachmentUpload');
    if (includeAttachment) {
        attachmentUpload.style.display = 'block';
    } else {
        attachmentUpload.style.display = 'none';
    }

    // 更新附件预览
    const attachmentPreview = document.getElementById('libeiAttachmentPreview');
    if (includeAttachment) {
        const uploadedFile = window.libeiUploadedFile;
        if (uploadedFile) {
            attachmentPreview.textContent = uploadedFile.original_name;
            attachmentPreview.className = 'text-success small';
        } else {
            attachmentPreview.textContent = '请先下载并上传Excel文件';
            attachmentPreview.className = 'text-warning small';
        }
    } else {
        attachmentPreview.textContent = '不包含附件';
        attachmentPreview.className = 'text-muted small';
    }
}

// 上传李贝附件
function uploadLibeiAttachment() {
    const fileInput = document.getElementById('libeiAttachmentFile');
    const statusDiv = document.getElementById('libeiAttachmentStatus');

    if (!fileInput.files || fileInput.files.length === 0) {
        return;
    }

    const file = fileInput.files[0];

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
        statusDiv.innerHTML = '<div class="alert alert-danger alert-sm">请选择Excel文件（.xlsx或.xls格式）</div>';
        return;
    }

    // 检查文件大小（10MB）
    if (file.size > 10 * 1024 * 1024) {
        statusDiv.innerHTML = '<div class="alert alert-danger alert-sm">文件大小不能超过10MB</div>';
        return;
    }

    // 显示上传中状态
    statusDiv.innerHTML = '<div class="alert alert-info alert-sm"><i class="bi bi-upload"></i> 正在上传...</div>';

    // 创建FormData
    const formData = new FormData();
    formData.append('attachment', file);

    // 上传文件
    fetch('/api/upload_attachment', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusDiv.innerHTML = `<div class="alert alert-success alert-sm"><i class="bi bi-check-circle"></i> 上传成功：${data.original_name}</div>`;
            // 保存上传的文件信息
            window.libeiUploadedFile = data;
            updateLibeiEmailPreview();
        } else {
            statusDiv.innerHTML = `<div class="alert alert-danger alert-sm"><i class="bi bi-x-circle"></i> 上传失败：${data.error}</div>`;
        }
    })
    .catch(error => {
        console.error('上传失败:', error);
        statusDiv.innerHTML = '<div class="alert alert-danger alert-sm"><i class="bi bi-x-circle"></i> 网络错误，上传失败</div>';
    });
}

// 保存李贝邮件配置
function saveLibeiEmailConfig() {
    const recipients = document.getElementById('libeiRecipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('libeiCc').value.split('\n').filter(e => e.trim());

    if (recipients.length === 0) {
        alert('请至少输入一个收件人邮箱地址');
        return;
    }

    // 发送保存请求
    fetch('/api/libei_email_config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            recipients: recipients,
            cc: cc
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('李贝邮件配置保存成功！', 'success');
        } else {
            showMessage('保存失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('保存李贝邮件配置失败:', error);
        showMessage('网络错误，保存失败', 'error');
    });
}

// 从模态框发送李贝邮件
function sendLibeiEmailFromModal() {
    const recipients = document.getElementById('libeiRecipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('libeiCc').value.split('\n').filter(e => e.trim());
    const includeAttachment = document.getElementById('includeLibeiAttachment').checked;

    if (recipients.length === 0) {
        alert('请至少输入一个收件人邮箱地址');
        return;
    }

    // 检查附件
    let attachmentFilename = null;
    if (includeAttachment) {
        const uploadedFile = window.libeiUploadedFile;
        if (!uploadedFile) {
            alert('请先下载李贝Excel文件并上传作为附件');
            return;
        }
        attachmentFilename = uploadedFile.filename;
    }

    // 显示加载状态
    showMessage('正在发送邮件...', 'info');

    // 使用新的李贝邮件API
    fetch('/api/send_libei_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            recipients: recipients,
            cc: cc.length > 0 ? cc : undefined,
            include_attachment: includeAttachment,
            attachment_filename: attachmentFilename
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('邮件发送成功！', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('libeiEmailModal'));
            modal.hide();
        } else {
            showMessage('邮件发送失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('邮件发送失败:', error);
        showMessage('网络错误，邮件发送失败', 'error');
    });
}

// 快速发送李贝邮件（使用默认邮箱，仅文本）
function sendLibeiEmailQuick(email) {
    // 显示加载状态
    showMessage('正在发送邮件（仅文本）...', 'info');

    // 使用新的李贝邮件API
    fetch('/api/send_libei_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            recipients: [email],
            include_attachment: false  // 快速发送不包含附件
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(`邮件发送成功！(${email})`, 'success');
        } else {
            showMessage('邮件发送失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('邮件发送失败:', error);
        showMessage('网络错误，邮件发送失败', 'error');
    });
}

// 添加事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 李贝邮件表单变化监听
    const libeiRecipientsInput = document.getElementById('libeiRecipients');
    const libeiCcInput = document.getElementById('libeiCc');
    const libeiAttachmentCheckbox = document.getElementById('includeLibeiAttachment');

    if (libeiRecipientsInput) {
        libeiRecipientsInput.addEventListener('input', updateLibeiEmailPreview);
    }
    if (libeiCcInput) {
        libeiCcInput.addEventListener('input', updateLibeiEmailPreview);
    }
    if (libeiAttachmentCheckbox) {
        libeiAttachmentCheckbox.addEventListener('change', updateLibeiEmailPreview);
    }
});

// 显示自定义邮件模态框
function showCustomEmailModal() {
    // 加载邮箱账户列表
    loadEmailAccountsForModal();

    // 加载邮件组合列表
    loadEmailCombinations();

    // 加载默认配置
    fetch('/api/email_config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.config;
                document.getElementById('recipients').value = config.default_recipients.join('\n');
                document.getElementById('cc').value = config.default_cc.join('\n');
                document.getElementById('bcc').value = config.default_bcc.join('\n');
                updateEmailPreview();
            }
        })
        .catch(error => {
            console.error('加载邮件配置失败:', error);
        });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('customEmailModal'));
    modal.show();

    // 初始化时间范围说明（因为默认选择了当月数据）
    setTimeout(() => {
        updateTrendTimeRangeOptions();
    }, 100);
}

// 加载邮箱账户列表（用于模态框）
function loadEmailAccountsForModal() {
    fetch('/api/get_email_accounts')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('emailAccountSelect');
                select.innerHTML = '';

                data.accounts.forEach(account => {
                    const option = document.createElement('option');
                    option.value = account.account_id;
                    option.textContent = `${account.name} (${account.email})`;
                    if (account.is_default) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });

                // 加载当前选中的邮箱账户信息
                updateEmailAccountInfo();
            } else {
                console.error('加载邮箱账户失败:', data.error);
            }
        })
        .catch(error => {
            console.error('加载邮箱账户失败:', error);
        });
}

// 更新邮箱账户信息显示
function updateEmailAccountInfo() {
    const select = document.getElementById('emailAccountSelect');
    const accountId = select.value;
    const infoDiv = document.getElementById('emailAccountInfo');
    const infoSpan = document.getElementById('currentEmailInfo');

    if (accountId) {
        const selectedOption = select.options[select.selectedIndex];
        infoSpan.textContent = selectedOption.textContent;
        infoDiv.style.display = 'block';

        // 设置当前邮箱账户
        fetch('/api/set_current_email_account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ account_id: accountId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('邮箱账户切换成功:', data.message);
            } else {
                console.error('邮箱账户切换失败:', data.error);
            }
        })
        .catch(error => {
            console.error('邮箱账户切换失败:', error);
        });
    } else {
        infoDiv.style.display = 'none';
    }
}

// 加载邮件组合列表
function loadEmailCombinations() {
    fetch('/api/email_combinations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('emailCombination');
                select.innerHTML = '<option value="">请选择邮件组合...</option>';

                data.combinations.forEach(combination => {
                    const option = document.createElement('option');
                    option.value = combination.id;
                    option.textContent = combination.name + (combination.is_default ? ' (默认)' : '');
                    if (combination.description) {
                        option.title = combination.description;
                    }
                    select.appendChild(option);

                    // 如果是默认组合，自动选择
                    if (combination.is_default) {
                        option.selected = true;
                        loadEmailCombination(); // 自动加载默认组合
                    }
                });
            }
        })
        .catch(error => {
            console.error('加载邮件组合失败:', error);
        });
}

// 加载选中的邮件组合
function loadEmailCombination() {
    const select = document.getElementById('emailCombination');
    const combinationId = select.value;

    if (!combinationId) {
        return;
    }

    fetch('/api/email_combinations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const combination = data.combinations.find(c => c.id === combinationId);
                if (combination) {
                    document.getElementById('recipients').value = combination.recipients.join('\n');
                    document.getElementById('cc').value = combination.cc.join('\n');
                    document.getElementById('bcc').value = combination.bcc.join('\n');
                    updateEmailPreview();

                    // 显示提示信息
                    showMessage(`已加载邮件组合: ${combination.name}`, 'success');
                }
            }
        })
        .catch(error => {
            console.error('加载邮件组合详情失败:', error);
        });
}

// 显示邮件组合管理器
function showEmailCombinationManager() {
    const modal = new bootstrap.Modal(document.getElementById('emailCombinationManagerModal'));
    modal.show();
    loadEmailCombinationsManager();
}

// 加载邮件组合管理器列表
function loadEmailCombinationsManager() {
    const container = document.getElementById('emailCombinationsList');
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">正在加载邮件组合...</div>
        </div>
    `;

    fetch('/api/email_combinations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.combinations.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <div class="mt-2">暂无邮件组合</div>
                            <div class="text-muted small">点击"添加新组合"创建第一个邮件组合</div>
                        </div>
                    `;
                    return;
                }

                let html = '';
                data.combinations.forEach(combination => {
                    const defaultBadge = combination.is_default ? '<span class="badge bg-primary ms-2">默认</span>' : '';
                    html += `
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="bi bi-people"></i> ${combination.name}${defaultBadge}
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editEmailCombination('${combination.id}')">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </button>
                                    ${!combination.is_default ? `
                                        <button class="btn btn-outline-success" onclick="setDefaultEmailCombination('${combination.id}')">
                                            <i class="bi bi-star"></i> 设为默认
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-outline-danger" onclick="deleteEmailCombination('${combination.id}', '${combination.name}')">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                ${combination.description ? `<p class="text-muted small mb-2">${combination.description}</p>` : ''}
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>收件人:</strong>
                                        <div class="small text-muted">${combination.recipients.join(', ') || '无'}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>抄送:</strong>
                                        <div class="small text-muted">${combination.cc.join(', ') || '无'}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>密送:</strong>
                                        <div class="small text-muted">${combination.bcc.join(', ') || '无'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 加载失败: ${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载邮件组合失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 网络错误，请稍后重试
                </div>
            `;
        });
}

// 显示添加组合表单
function showAddCombinationForm() {
    document.getElementById('combinationFormTitle').textContent = '添加新邮件组合';
    document.getElementById('combinationId').value = '';
    document.getElementById('emailCombinationForm').reset();
    document.getElementById('combinationForm').style.display = 'block';
}

// 隐藏组合表单
function hideCombinationForm() {
    document.getElementById('combinationForm').style.display = 'none';
}

// 编辑邮件组合
function editEmailCombination(combinationId) {
    fetch('/api/email_combinations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const combination = data.combinations.find(c => c.id === combinationId);
                if (combination) {
                    document.getElementById('combinationFormTitle').textContent = '编辑邮件组合';
                    document.getElementById('combinationId').value = combination.id;
                    document.getElementById('combinationName').value = combination.name;
                    document.getElementById('combinationDescription').value = combination.description;
                    document.getElementById('combinationRecipients').value = combination.recipients.join('\n');
                    document.getElementById('combinationCc').value = combination.cc.join('\n');
                    document.getElementById('combinationBcc').value = combination.bcc.join('\n');
                    document.getElementById('combinationIsDefault').checked = combination.is_default;
                    document.getElementById('combinationForm').style.display = 'block';
                }
            }
        })
        .catch(error => {
            console.error('加载组合详情失败:', error);
            showMessage('加载组合详情失败', 'error');
        });
}

// 删除邮件组合
function deleteEmailCombination(combinationId, combinationName) {
    if (!confirm(`确定要删除邮件组合"${combinationName}"吗？此操作不可撤销。`)) {
        return;
    }

    fetch(`/api/email_combinations/${combinationId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            loadEmailCombinationsManager();
            loadEmailCombinations(); // 刷新选择框
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('删除组合失败:', error);
        showMessage('删除组合失败', 'error');
    });
}

// 设置默认邮件组合
function setDefaultEmailCombination(combinationId) {
    fetch(`/api/email_combinations/${combinationId}/set_default`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            loadEmailCombinationsManager();
            loadEmailCombinations(); // 刷新选择框
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('设置默认组合失败:', error);
        showMessage('设置默认组合失败', 'error');
    });
}

// 更新邮件预览
function updateEmailPreview() {
    const recipients = document.getElementById('recipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('cc').value.split('\n').filter(e => e.trim());
    const bcc = document.getElementById('bcc').value.split('\n').filter(e => e.trim());

    document.getElementById('recipientsPreview').textContent =
        recipients.length > 0 ? recipients.join(', ') : '未设置';
    document.getElementById('ccPreview').textContent =
        cc.length > 0 ? cc.join(', ') : '未设置';
    document.getElementById('bccPreview').textContent =
        bcc.length > 0 ? bcc.join(', ') : '未设置';
}

// 邮件组合表单提交处理
document.addEventListener('DOMContentLoaded', function() {
    const combinationForm = document.getElementById('emailCombinationForm');
    if (combinationForm) {
        combinationForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const combinationId = document.getElementById('combinationId').value;
            const formData = {
                name: document.getElementById('combinationName').value.trim(),
                description: document.getElementById('combinationDescription').value.trim(),
                recipients: document.getElementById('combinationRecipients').value.split('\n').filter(email => email.trim()),
                cc: document.getElementById('combinationCc').value.split('\n').filter(email => email.trim()),
                bcc: document.getElementById('combinationBcc').value.split('\n').filter(email => email.trim()),
                is_default: document.getElementById('combinationIsDefault').checked
            };

            if (!formData.name) {
                showMessage('请输入组合名称', 'error');
                return;
            }

            if (formData.recipients.length === 0) {
                showMessage('请至少输入一个收件人', 'error');
                return;
            }

            const url = combinationId ? `/api/email_combinations/${combinationId}` : '/api/email_combinations';
            const method = combinationId ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    hideCombinationForm();
                    loadEmailCombinationsManager();
                    loadEmailCombinations(); // 刷新选择框
                } else {
                    showMessage(data.error, 'error');
                }
            })
            .catch(error => {
                console.error('保存组合失败:', error);
                showMessage('保存组合失败', 'error');
            });
        });
    }
});

// 更新趋势图选项显示
function updateTrendChartsOptions() {
    const includeTrendCharts = document.getElementById('includeTrendCharts').checked;
    const optionsDiv = document.getElementById('trendChartsOptions');

    if (includeTrendCharts) {
        optionsDiv.style.display = 'block';
    } else {
        optionsDiv.style.display = 'none';
    }
}

// 更新三合一选项显示（钉钉推送）
function updateCombineAllChartsOption() {
    const includeTrends = document.getElementById('includeTrends').checked;
    const combineContainer = document.getElementById('combineAllChartsContainer');
    const combineCheckbox = document.getElementById('combineAllCharts');

    if (includeTrends) {
        combineContainer.style.display = 'block';
    } else {
        combineContainer.style.display = 'none';
        combineCheckbox.checked = false; // 取消勾选三合一选项
    }
}

// 更新趋势图时间范围选项
function updateTrendTimeRangeOptions() {
    const timeRange = document.getElementById('trendChartTimeRange').value;
    const customDaysContainer = document.getElementById('customDaysContainer');
    const timeRangeText = document.getElementById('timeRangeText');

    // 显示/隐藏自定义天数选择
    if (timeRange === 'custom_days') {
        customDaysContainer.style.display = 'block';
    } else {
        customDaysContainer.style.display = 'none';
    }

    // 更新说明文字
    switch (timeRange) {
        case 'all':
            timeRangeText.textContent = '显示数据库中所有可用数据的趋势图';
            break;
        case 'current_month':
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();
            timeRangeText.textContent = `显示${currentYear}年${currentMonth}月的日度数据趋势图（从${currentMonth}月1日到当前日期）`;
            break;
        case 'custom_days':
            const days = document.getElementById('trendChartDays').value;
            timeRangeText.textContent = `显示最近${days}天的数据趋势图`;
            break;
    }
}

// 更新扇区数据附件预览
function updateSectorAttachmentPreview() {
    const includeSectorAttachment = document.getElementById('includeSectorAttachment').checked;
    const previewDiv = document.getElementById('sectorAttachmentPreview');

    if (includeSectorAttachment) {
        previewDiv.style.display = 'block';
    } else {
        previewDiv.style.display = 'none';
    }
}

// 更新重复故障分析附件预览
function updateRepeatedOutagesAttachmentPreview() {
    const includeRepeatedOutages = document.getElementById('includeRepeatedOutagesAttachment').checked;
    const previewDiv = document.getElementById('repeatedOutagesAttachmentPreview');
    const paramsDiv = document.getElementById('repeatedOutagesParams');

    if (includeRepeatedOutages) {
        previewDiv.style.display = 'block';
        paramsDiv.style.display = 'block';

        // 自动设置默认目标日期为数据库最新日期
        loadLatestDateForRepeatedOutages();
    } else {
        previewDiv.style.display = 'none';
        paramsDiv.style.display = 'none';
    }
}

// 切换重复故障分析模式
function toggleRepeatedAnalysisMode() {
    const mode = document.getElementById('repeatedAnalysisMode').value;
    const manualRow = document.getElementById('repeatedManualDaysRow');

    if (mode === 'manual') {
        manualRow.style.display = 'block';
    } else {
        manualRow.style.display = 'none';
    }
}

// 获取数据库最新日期用于重复故障分析
function loadLatestDateForRepeatedOutages() {
    fetch('/api/get_latest_date', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success && result.latest_date) {
            document.getElementById('repeatedTargetDate').value = result.latest_date;
        } else {
            // 如果获取失败，使用今天的日期作为备选
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('repeatedTargetDate').value = today;
        }
    })
    .catch(error => {
        // 如果请求失败，使用今天的日期作为备选
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('repeatedTargetDate').value = today;
        console.error('获取最新日期失败:', error);
    });
}

// 保存邮件配置
function saveEmailConfig() {
    const recipients = document.getElementById('recipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('cc').value.split('\n').filter(e => e.trim());
    const bcc = document.getElementById('bcc').value.split('\n').filter(e => e.trim());

    if (recipients.length === 0) {
        alert('请至少输入一个收件人邮箱地址');
        return;
    }

    // 发送保存请求
    fetch('/api/email_config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            default_recipients: recipients,
            default_cc: cc,
            default_bcc: bcc
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('邮件配置保存成功！', 'success');
        } else {
            showMessage('保存失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        showMessage('网络错误，保存失败', 'error');
    });
}

// 全局变量存储上传的附件
let uploadedAttachment = null;

// 处理附件上传
function handleAttachmentUpload(input) {
    const file = input.files[0];
    if (!file) {
        removeAttachment();
        return;
    }

    // 检查文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        alert('文件大小不能超过10MB');
        input.value = '';
        removeAttachment();
        return;
    }

    // 检查文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'application/pdf', // .pdf
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'text/plain', // .txt
        'text/csv' // .csv
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|pdf|docx|doc|txt|csv)$/i)) {
        alert('不支持的文件格式，请选择Excel、PDF、Word或文本文件');
        input.value = '';
        removeAttachment();
        return;
    }

    // 上传文件到服务器
    const formData = new FormData();
    formData.append('attachment', file);

    fetch('/api/upload_attachment', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedAttachment = {
                filename: data.filename,
                original_name: file.name,
                size: file.size
            };
            showAttachmentPreview(file.name, file.size);
            showMessage('附件上传成功！', 'success');
        } else {
            showMessage('附件上传失败：' + (data.error || '未知错误'), 'error');
            input.value = '';
            removeAttachment();
        }
    })
    .catch(error => {
        console.error('附件上传失败:', error);
        showMessage('网络错误，附件上传失败', 'error');
        input.value = '';
        removeAttachment();
    });
}

// 显示附件预览
function showAttachmentPreview(fileName, fileSize) {
    const preview = document.getElementById('attachmentPreview');
    const nameSpan = document.getElementById('attachmentName');

    const sizeText = fileSize < 1024 * 1024 ?
        `${(fileSize / 1024).toFixed(1)} KB` :
        `${(fileSize / (1024 * 1024)).toFixed(1)} MB`;

    nameSpan.textContent = `${fileName} (${sizeText})`;
    preview.style.display = 'block';
}

// 移除附件
function removeAttachment() {
    uploadedAttachment = null;
    document.getElementById('emailAttachment').value = '';
    document.getElementById('attachmentPreview').style.display = 'none';

    // 如果有上传的文件，通知服务器删除
    // 这里可以添加删除服务器文件的逻辑
}

// 监听输入变化，实时更新预览
document.addEventListener('DOMContentLoaded', function() {
    ['recipients', 'cc', 'bcc'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateEmailPreview);
        }
    });
});

// 发送自定义邮件
function sendCustomEmail() {
    addDebugLog('🚀 开始自定义邮件发送', 'info');

    const form = document.getElementById('customEmailForm');
    const recipients = document.getElementById('recipients').value.split('\n').filter(e => e.trim());
    const cc = document.getElementById('cc').value.split('\n').filter(e => e.trim());
    const bcc = document.getElementById('bcc').value.split('\n').filter(e => e.trim());
    const emailType = 'html';  // 固定使用HTML邮件类型
    const emailAccountId = document.getElementById('emailAccountSelect').value;  // 获取选中的邮箱账户ID

    addDebugLog(`📧 收件人: ${recipients.join(', ')}`, 'info');
    if (cc.length > 0) addDebugLog(`📧 抄送: ${cc.join(', ')}`, 'info');
    if (bcc.length > 0) addDebugLog(`📧 密送: ${bcc.length} 人`, 'info');

    // 获取趋势图选项
    const includeTrendCharts = document.getElementById('includeTrendCharts').checked;
    const trendChartTimeRange = document.getElementById('trendChartTimeRange').value;
    const trendChartDays = parseInt(document.getElementById('trendChartDays').value);
    const includeTargetLine = document.getElementById('includeTargetLine').checked;
    const chartMode = 'static';  // 固定使用静态内嵌模式

    // 获取扇区数据附件选项
    const includeSectorAttachment = document.getElementById('includeSectorAttachment').checked;

    // 获取重复故障分析附件选项
    const includeRepeatedOutages = document.getElementById('includeRepeatedOutagesAttachment').checked;
    let repeatedOutagesOptions = {};

    if (includeRepeatedOutages) {
        repeatedOutagesOptions = {
            target_date: document.getElementById('repeatedTargetDate').value,
            analysis_mode: document.getElementById('repeatedAnalysisMode').value,
            min_occurrences: parseInt(document.getElementById('repeatedMinOccurrences').value)
        };

        if (repeatedOutagesOptions.analysis_mode === 'manual') {
            repeatedOutagesOptions.days_back = parseInt(document.getElementById('repeatedDaysBack').value);
        }
    }

    addDebugLog(`⚙️ 邮件选项: 趋势图=${includeTrendCharts}, 扇区附件=${includeSectorAttachment}, 重复故障附件=${includeRepeatedOutages}`, 'info');

    if (recipients.length === 0) {
        addDebugLog('❌ 收件人为空', 'error');
        alert('请至少输入一个收件人邮箱地址');
        return;
    }

    // 获取当前日期
    {% if report_info and report_info.date %}
    const reportDate = '{{ report_info.date }}';
    {% elif date_info and date_info.daily and date_info.daily.date %}
    const reportDate = '{{ date_info.daily.date }}';
    {% else %}
    const reportDate = null;
    {% endif %}

    addDebugLog(`📅 报表日期: ${reportDate}`, 'info');

    if (!reportDate) {
        addDebugLog('❌ 无法获取报表日期', 'error');
        alert('无法获取报表日期，邮件发送失败');
        return;
    }

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('customEmailModal'));
    modal.hide();

    addDebugLog('📤 准备发送自定义邮件...', 'info');

    // 发送邮件
    sendEmailWithCustomRecipients(emailType, recipients, cc, bcc, reportDate, {
        includeTrendCharts: includeTrendCharts,
        trendChartTimeRange: trendChartTimeRange,
        trendChartDays: trendChartDays,
        includeTargetLine: includeTargetLine,
        chartMode: chartMode,
        includeSectorAttachment: includeSectorAttachment,
        includeRepeatedOutagesAttachment: includeRepeatedOutages,
        repeatedOutagesOptions: repeatedOutagesOptions,
        emailAccountId: emailAccountId  // 传递邮箱账户ID
    });
}

// 发送邮件（带自定义收件人）
function sendEmailWithCustomRecipients(type, recipients, cc, bcc, reportDate, trendOptions = {}) {
    addDebugLog('📨 开始发送自定义收件人邮件', 'info');

    // 注意：这里可能没有emailDropdown按钮，因为是从自定义模态框调用的
    // 我们需要找到正确的按钮或者创建一个虚拟的状态管理
    let btn = document.querySelector('.btn-primary[onclick*="sendQuickEmail"]');
    if (!btn) {
        // 如果找不到快速发送按钮，尝试找其他邮件相关按钮
        btn = document.querySelector('button[onclick*="sendCustomEmail"]');
    }

    let originalText = '';
    if (btn) {
        originalText = btn.innerHTML;
        // 禁用按钮并显示加载状态
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';
    }

    const requestData = {
        date: reportDate,
        type: type,
        recipients: recipients,
        cc: cc,
        bcc: bcc,
        attachment: uploadedAttachment ? uploadedAttachment.filename : undefined,
        includeTrendCharts: trendOptions.includeTrendCharts || false,
        trendChartTimeRange: trendOptions.trendChartTimeRange || 'all',
        trendChartDays: trendOptions.trendChartDays || 30,
        chartMode: trendOptions.chartMode || 'static',
        includeSectorAttachment: trendOptions.includeSectorAttachment || false,
        includeRepeatedOutagesAttachment: trendOptions.includeRepeatedOutagesAttachment || false,
        repeatedOutagesOptions: trendOptions.repeatedOutagesOptions || {},
        emailAccountId: trendOptions.emailAccountId  // 添加邮箱账户ID
    };

    addDebugLog(`📋 请求数据: ${JSON.stringify(requestData, null, 2)}`, 'info');

    // 发送请求
    fetch('/api/send_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        addDebugLog(`📡 收到服务器响应，状态码: ${response.status}`, 'info');
        return response.json();
    })
    .then(data => {
        addDebugLog(`📋 解析响应数据: ${JSON.stringify(data)}`, 'info');

        if (data.success) {
            // 成功
            addDebugLog('✅ 自定义邮件发送成功！', 'success');

            if (btn) {
                btn.innerHTML = '<i class="bi bi-check-circle"></i> 发送成功';
                btn.className = 'btn btn-success';
            }

            // 显示成功消息
            let message = `邮件发送成功！\n收件人: ${recipients.join(', ')}`;
            if (cc.length > 0) {
                message += `\n抄送: ${cc.join(', ')}`;
            }
            if (bcc.length > 0) {
                message += `\n密送: ${bcc.length} 人`;
            }
            if (data.details && data.details.length > 0) {
                message += '\n详情：\n' + data.details.join('\n');
            }
            showMessage(message, 'success');

            // 3秒后恢复按钮状态
            setTimeout(() => {
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-primary';
                }
            }, 3000);
        } else {
            // 失败
            addDebugLog(`❌ 自定义邮件发送失败: ${data.error || data.message || '未知错误'}`, 'error');

            if (btn) {
                btn.innerHTML = '<i class="bi bi-x-circle"></i> 发送失败';
                btn.className = 'btn btn-danger';
            }

            // 显示详细错误消息
            let message = '邮件发送失败：' + (data.error || data.message || '未知错误');
            if (data.details && data.details.length > 0) {
                message += '\n详情：\n' + data.details.join('\n');
            }
            showMessage(message, 'error');

            // 3秒后恢复按钮状态
            setTimeout(() => {
                if (btn) {
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                    btn.className = 'btn btn-primary';
                }
            }, 3000);
        }
    })
    .catch(error => {
        addDebugLog(`🔥 自定义邮件网络请求失败: ${error.message}`, 'error');
        console.error('邮件发送失败:', error);

        if (btn) {
            btn.innerHTML = '<i class="bi bi-x-circle"></i> 网络错误';
            btn.className = 'btn btn-danger';
        }

        showMessage('网络错误，邮件发送失败', 'error');

        // 3秒后恢复按钮状态
        setTimeout(() => {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = originalText;
                btn.className = 'btn btn-primary';
            }
        }, 3000);
    });
}

// 邮件发送功能（使用默认收件人）
function sendToEmail(type = 'text_image') {
    const btn = document.getElementById('emailDropdown');
    const originalText = btn.innerHTML;

    // 获取当前日期
    {% if report_info and report_info.date %}
    const reportDate = '{{ report_info.date }}';
    {% elif date_info and date_info.daily and date_info.daily.date %}
    const reportDate = '{{ date_info.daily.date }}';
    {% else %}
    const reportDate = null;
    {% endif %}

    if (!reportDate) {
        alert('无法获取报表日期，邮件发送失败');
        return;
    }

    // 禁用按钮并显示加载状态
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';

    // 发送请求
    fetch('/api/send_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: reportDate,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 成功
            btn.innerHTML = '<i class="bi bi-check-circle"></i> 发送成功';
            btn.className = 'btn btn-success btn-sm dropdown-toggle';

            // 显示成功消息
            let message = '邮件发送成功！';
            if (data.details && data.details.length > 0) {
                message += '\n详情：\n' + data.details.join('\n');
            }
            showMessage(message, 'success');

            // 3秒后恢复按钮状态
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
                btn.className = 'btn btn-outline-light btn-sm dropdown-toggle';
            }, 3000);
        } else {
            // 失败
            btn.innerHTML = '<i class="bi bi-x-circle"></i> 发送失败';
            btn.className = 'btn btn-danger btn-sm dropdown-toggle';

            // 显示详细错误消息
            let message = '邮件发送失败：' + (data.error || '未知错误');
            if (data.details && data.details.length > 0) {
                message += '\n详情：\n' + data.details.join('\n');
            }
            showMessage(message, 'error');

            // 3秒后恢复按钮状态
            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
                btn.className = 'btn btn-outline-light btn-sm dropdown-toggle';
            }, 3000);
        }
    })
    .catch(error => {
        console.error('邮件发送失败:', error);

        btn.innerHTML = '<i class="bi bi-x-circle"></i> 网络错误';
        btn.className = 'btn btn-danger btn-sm dropdown-toggle';

        showMessage('网络错误，邮件发送失败', 'error');

        // 3秒后恢复按钮状态
        setTimeout(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
            btn.className = 'btn btn-outline-light btn-sm dropdown-toggle';
        }, 3000);
    });
}

// 显示消息提示
function showMessage(message, type) {
    // 创建消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 99999; min-width: 300px; max-width: 500px;';

    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// 表格行高亮
document.querySelectorAll('tbody tr').forEach(row => {
    row.addEventListener('mouseenter', function() {
        if (!this.classList.contains('table-warning')) {
            this.style.backgroundColor = '#f8f9fa';
        }
    });
    
    row.addEventListener('mouseleave', function() {
        if (!this.classList.contains('table-warning')) {
            this.style.backgroundColor = '';
        }
    });
});

// 加载钉钉API和图床选择列表
function loadApiAndHostOptions() {
    // 加载钉钉API列表（使用数据库接口）
    fetch('/api/get_all_dingtalk_apis')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const apiList = document.getElementById('dingtalkApiList');
                apiList.innerHTML = '';

                Object.entries(data.apis).forEach(([apiId, config]) => {
                    const li = document.createElement('li');
                    const isCurrentApi = apiId === data.current;
                    li.innerHTML = `
                        <a class="dropdown-item ${isCurrentApi ? 'active' : ''}" href="#" onclick="switchDingtalkApi('${apiId}')">
                            <i class="bi bi-${isCurrentApi ? 'check-circle-fill text-success' : 'circle'}"></i>
                            ${config.name} ${config.enabled ? '' : '(未启用)'}
                        </a>
                    `;
                    apiList.appendChild(li);
                });

                console.log('钉钉API加载成功:', Object.keys(data.apis).length, '个API');
            } else {
                console.error('加载钉钉API失败:', data.error);
            }
        })
        .catch(error => console.error('加载钉钉API失败:', error));

    // 加载图床列表
    fetch('/api/get_image_hosts')
        .then(response => response.json())
        .then(data => {
            const hostList = document.getElementById('imageHostList');
            hostList.innerHTML = '';

            Object.entries(data.hosts).forEach(([hostId, config]) => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <a class="dropdown-item ${config.is_current ? 'active' : ''}" href="#" onclick="switchImageHost('${hostId}')">
                        <i class="bi bi-${config.is_current ? 'check-circle-fill text-success' : 'circle'}"></i>
                        ${config.name} ${config.enabled ? '' : '(未启用)'}
                    </a>
                `;
                hostList.appendChild(li);
            });
        })
        .catch(error => console.error('加载图床列表失败:', error));
}

// 切换钉钉API
function switchDingtalkApi(apiId) {
    fetch('/api/set_dingtalk_api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ api_id: apiId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            loadApiAndHostOptions(); // 重新加载列表
        } else {
            showMessage('切换失败：' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('切换钉钉API失败:', error);
        showMessage('切换失败：网络错误', 'error');
    });
}

// 切换图床
function switchImageHost(hostId) {
    fetch('/api/set_image_host', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ host_id: hostId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            loadApiAndHostOptions(); // 重新加载列表
        } else {
            showMessage('切换失败：' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('切换图床失败:', error);
        showMessage('切换失败：网络错误', 'error');
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadApiAndHostOptions();
    loadColorOptions();
    loadFontSizeOptions();
    loadDataBoldOptions();
});











// 获取当前钉钉API
function getCurrentDingtalkApi() {
    const currentApi = document.querySelector('input[name="trendsApiChoice"]:checked');
    return currentApi ? currentApi.value : null;
}

// 切换钉钉API（带回调）
function switchDingtalkApiWithCallback(apiId, callback) {
    fetch('/api/set_dingtalk_api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_id: apiId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('钉钉API切换成功:', data.message);
            // 如果没有回调函数，显示用户提示并重新加载列表
            if (!callback) {
                showMessage(data.message, 'success');
                loadApiAndHostOptions(); // 重新加载列表以更新状态
            }
            if (callback) callback();
        } else {
            if (callback) {
                showTrendsResult('danger', 'API切换失败', data.error);
            } else {
                showMessage('切换失败：' + data.error, 'error');
            }
        }
    })
    .catch(error => {
        console.error('钉钉API切换失败:', error);
        if (callback) {
            showTrendsResult('danger', 'API切换失败', error.message);
        } else {
            showMessage('切换失败：网络错误', 'error');
        }
    });
}

// 钉钉推送功能
function sendToDingtalk(sendMethod = 'multiple_images') {
    return new Promise((resolve, reject) => {
        // 显示发送方式
        const methodNames = {
            'multiple_images': '多图片合并发送',
            'separate_images': '分别发送图片',
            'text_only': '仅发送文本',
            'native_upload': '钉钉原生上传'
        };

        const methodName = methodNames[sendMethod] || '多图片合并发送';
        console.log(`使用发送方式: ${methodName}`);

        // 显示确认消息
        showMessage(`正在使用 "${methodName}" 方式发送钉钉消息...`, 'info');

        // 获取API类型和趋势图选项
        const apiType = document.getElementById('globalApiType').value;
        const includeTrends = document.getElementById('includeTrends').checked;
        const dingtalkTrendTimeRange = document.getElementById('dingtalkTrendTimeRange').value;
        const reportFormat = document.getElementById('reportFormat').value;
        const imageUploadMethod = document.getElementById('imageUploadMethod').value;
        const combineAllCharts = document.getElementById('combineAllCharts').checked;

        // 发送钉钉推送请求
        fetch('/api/send_dingtalk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                send_method: sendMethod,
                api_type: apiType,
                include_trends: includeTrends,
                trend_time_range: dingtalkTrendTimeRange,
                report_format: reportFormat,
                image_upload_method: imageUploadMethod,
                combine_all_charts: combineAllCharts
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示成功消息
                let message = `钉钉推送成功！使用方式：${methodName}`;
                if (data.details && data.details.length > 0) {
                    message += '\n详情：\n' + data.details.join('\n');
                }
                showMessage(message, 'success');

                // 显示详细调试信息
                if (data.debug_info) {
                    const debug = data.debug_info;
                    addDebugLog('🔍 DEBUG: ===== 钉钉发送详细信息 =====', 'info');
                    addDebugLog(`🔍 DEBUG: 数据库连接: ${debug.database_connected ? '✅' : '❌'}`, 'info');
                    addDebugLog(`🔍 DEBUG: 日度数据行数: ${debug.daily_data_rows}`, 'info');
                    addDebugLog(`🔍 DEBUG: 使用日期: ${debug.date_used}`, 'info');
                    addDebugLog(`🔍 DEBUG: 综合报表图片: ${debug.comprehensive_image_generated ? '✅' : '❌'}`, 'info');
                    addDebugLog(`🔍 DEBUG: 扇区数据图片: ${debug.sector_image_generated ? '✅' : '❌'}`, 'info');
                    addDebugLog(`🔍 DEBUG: 包含趋势图: ${debug.trends_included ? '✅' : '❌'}`, 'info');
                    addDebugLog(`🔍 DEBUG: 图片上传方式: ${debug.image_upload_method}`, 'info');
                    addDebugLog(`🔍 DEBUG: 总图片数量: ${debug.total_images}`, 'info');
                    addDebugLog(`🔍 DEBUG: 预期图片数量: ${debug.expected_images}`, 'info');

                    if (debug.image_details) {
                        addDebugLog(`🔍 DEBUG: 外部图床图片: ${debug.image_details.external_images}`, 'info');
                        addDebugLog(`🔍 DEBUG: 钉钉原生图片: ${debug.image_details.dingtalk_images}`, 'info');

                        if (debug.image_details.external_urls.length > 0) {
                            debug.image_details.external_urls.forEach(url => {
                                addDebugLog(`✅ DEBUG: ${url}`, 'success');
                            });
                        }

                        if (debug.image_details.dingtalk_media_ids.length > 0) {
                            debug.image_details.dingtalk_media_ids.forEach(media => {
                                addDebugLog(`✅ DEBUG: ${media}`, 'success');
                            });
                        }
                    }

                    if (debug.trends_error) {
                        addDebugLog(`❌ DEBUG: 趋势图错误: ${debug.trends_error}`, 'error');
                    }

                    addDebugLog('🔍 DEBUG: ===== 调试信息结束 =====', 'info');
                }

                resolve(data);
            } else {
                // 显示详细错误消息
                let message = `钉钉推送失败（${methodName}）：` + (data.error || '未知错误');
                if (data.details && data.details.length > 0) {
                    message += '\n详情：\n' + data.details.join('\n');
                }
                showMessage(message, 'error');
                reject(new Error(data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('钉钉推送失败:', error);
            showMessage(`网络错误，钉钉推送失败（${methodName}）`, 'error');
            reject(error);
        });
    });
}

// 加载颜色选择选项
function loadColorOptions() {
    fetch('/api/get_dingtalk_colors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const colorSelector = document.getElementById('colorSelector');
                colorSelector.innerHTML = '';

                // 创建颜色按钮网格
                const colorGrid = document.createElement('div');
                colorGrid.className = 'row g-1';

                Object.entries(data.colors).forEach(([colorId, config]) => {
                    const col = document.createElement('div');
                    col.className = 'col-6';

                    const button = document.createElement('button');
                    button.className = `btn btn-sm w-100 ${config.is_current ? 'btn-primary' : 'btn-outline-secondary'}`;
                    button.style.borderColor = config.value;
                    if (!config.is_current) {
                        button.style.color = config.value;
                    }
                    button.innerHTML = `
                        <span class="color-dot me-1" style="display: inline-block; width: 12px; height: 12px; border-radius: 50%; background-color: ${config.value};"></span>
                        ${config.name}
                    `;
                    button.onclick = () => setDingtalkColor(colorId);

                    col.appendChild(button);
                    colorGrid.appendChild(col);
                });

                colorSelector.appendChild(colorGrid);
            }
        })
        .catch(error => {
            console.error('加载颜色选项失败:', error);
        });
}

// 设置钉钉颜色
function setDingtalkColor(colorId) {
    fetch('/api/set_dingtalk_color', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            color_id: colorId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 重新加载颜色选项以更新当前选择
            loadColorOptions();
        } else {
            showMessage('设置颜色失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('设置颜色失败:', error);
        showMessage('网络错误，设置颜色失败', 'error');
    });
}

// 加载字体大小选择选项
function loadFontSizeOptions() {
    fetch('/api/get_font_sizes')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const fontSizeSelector = document.getElementById('fontSizeSelector');
                fontSizeSelector.innerHTML = '';

                // 创建字体大小按钮网格
                const sizeGrid = document.createElement('div');
                sizeGrid.className = 'row g-1';

                Object.entries(data.sizes).forEach(([sizeId, config]) => {
                    const col = document.createElement('div');
                    col.className = 'col-6';

                    const button = document.createElement('button');
                    button.className = `btn btn-sm w-100 ${config.is_current ? 'btn-success' : 'btn-outline-secondary'}`;
                    button.innerHTML = `
                        <span class="me-1">${config.name}</span>
                        <small class="text-muted">(${config.data}px)</small>
                    `;
                    button.onclick = () => setFontSize(sizeId);

                    col.appendChild(button);
                    sizeGrid.appendChild(col);
                });

                fontSizeSelector.appendChild(sizeGrid);
            }
        })
        .catch(error => {
            console.error('加载字体大小选项失败:', error);
        });
}

// 设置字体大小
function setFontSize(sizeId) {
    fetch('/api/set_font_size', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            size_id: sizeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 重新加载字体大小选项以更新当前选择
            loadFontSizeOptions();
        } else {
            showMessage('设置字体大小失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('设置字体大小失败:', error);
        showMessage('网络错误，设置字体大小失败', 'error');
    });
}

// 加载数据加粗选择选项
function loadDataBoldOptions() {
    fetch('/api/get_data_bold_options')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dataBoldSelector = document.getElementById('dataBoldSelector');
                dataBoldSelector.innerHTML = '';

                // 创建加粗选项按钮
                const boldGrid = document.createElement('div');
                boldGrid.className = 'd-grid gap-1';

                Object.entries(data.options).forEach(([optionId, config]) => {
                    const button = document.createElement('button');
                    button.className = `btn btn-sm ${config.is_current ? 'btn-warning' : 'btn-outline-secondary'}`;

                    // 根据加粗类型添加图标
                    let icon = '';
                    if (config.bold === false) {
                        icon = '📝'; // 不加粗
                    } else if (config.bold === true) {
                        icon = '💪'; // 全部加粗
                    } else if (config.bold === 'selective') {
                        icon = '🎯'; // 重要数据加粗
                    }

                    button.innerHTML = `${icon} ${config.name}`;
                    button.onclick = () => setDataBold(optionId);

                    boldGrid.appendChild(button);
                });

                dataBoldSelector.appendChild(boldGrid);
            }
        })
        .catch(error => {
            console.error('加载数据加粗选项失败:', error);
        });
}

// 设置数据加粗方式
function setDataBold(optionId) {
    fetch('/api/set_data_bold', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            option_id: optionId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 重新加载数据加粗选项以更新当前选择
            loadDataBoldOptions();
        } else {
            showMessage('设置数据加粗失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('设置数据加粗失败:', error);
        showMessage('网络错误，设置数据加粗失败', 'error');
    });
}

// 发送特殊格式邮件
function sendLibeiEmail() {
    addDebugLog('💌 开始李贝邮件发送流程', 'info');

    if (!confirm('确定要发送特殊格式邮件吗？\n\n此邮件使用当月日均故障数量计算差值：\n差值 = 当日目标 - 当月日均故障数量\n当月日均故障数量 = 月累计 ÷ 当前天数')) {
        addDebugLog('❌ 用户取消李贝邮件发送', 'warning');
        return;
    }

    // 获取当前日期
    const currentDate = document.querySelector('[data-date]')?.getAttribute('data-date') ||
                       new Date().toISOString().split('T')[0];
    addDebugLog(`📅 使用日期: ${currentDate}`, 'info');

    addDebugLog('⚙️ 获取李贝邮箱配置...', 'info');
    showMessage('正在获取李贝邮箱配置...', 'info');

    // 先获取李贝邮箱配置
    fetch('/api/get_libei_config')
        .then(response => response.json())
        .then(configData => {
            if (!configData.success) {
                showMessage('获取李贝邮箱配置失败', 'error');
                return;
            }

            const libeiEmail = configData.email;
            showMessage('正在发送特殊格式邮件...', 'info');

            // 调用现有的邮件API，但添加李贝标识（完全复制普通邮件的参数）
            fetch('/api/send_email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    date: currentDate,
                    type: 'html',
                    is_libei_email: true,  // 李贝邮件标识
                    recipients: [libeiEmail],  // 使用配置的李贝邮箱地址
                    attachment: uploadedAttachment ? uploadedAttachment.filename : undefined  // 复制附件逻辑
                })
            })
            .then(response => {
                addDebugLog(`📡 收到李贝邮件响应，状态码: ${response.status}`, 'info');
                return response.json();
            })
            .then(data => {
                addDebugLog(`📋 解析李贝邮件响应: ${JSON.stringify(data)}`, 'info');
                if (data.success) {
                    addDebugLog('✅ 李贝邮件发送成功！', 'success');
                    showMessage(`特殊格式邮件发送成功！\n收件人：${libeiEmail}\n日期：${currentDate}`, 'success');
                } else {
                    addDebugLog(`❌ 李贝邮件发送失败: ${data.error || '未知错误'}`, 'error');
                    showMessage('李贝邮件发送失败：' + (data.error || '未知错误'), 'error');
                }
            })
            .catch(error => {
                addDebugLog(`🔥 李贝邮件网络请求失败: ${error.message}`, 'error');
                console.error('李贝邮件发送失败:', error);
                showMessage('网络错误，李贝邮件发送失败', 'error');
            });
        })
        .catch(error => {
            console.error('获取李贝配置失败:', error);
            showMessage('获取李贝邮箱配置失败', 'error');
        });
}

// 显示李贝邮箱配置模态框
function showLibeiConfigModal() {
    // 加载当前配置
    loadLibeiConfig();
    const modal = new bootstrap.Modal(document.getElementById('libeiConfigModal'));
    modal.show();
}

// 加载李贝邮箱配置
function loadLibeiConfig() {
    fetch('/api/get_libei_config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('libeiEmail').value = data.email || '';
            }
        })
        .catch(error => {
            console.error('加载李贝配置失败:', error);
            // 使用默认值
            document.getElementById('libeiEmail').value = '<EMAIL>';
        });
}

// 保存李贝邮箱配置
function saveLibeiConfig() {
    const email = document.getElementById('libeiEmail').value.trim();

    if (!email) {
        showMessage('请输入李贝的邮箱地址', 'error');
        return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showMessage('请输入有效的邮箱地址', 'error');
        return;
    }

    fetch('/api/set_libei_config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('李贝邮箱配置保存成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('libeiConfigModal'));
            modal.hide();
        } else {
            showMessage('保存失败：' + (data.error || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('保存李贝配置失败:', error);
        showMessage('网络错误，保存失败', 'error');
    });
}

// 显示李贝数据预览模态框
function showLibeiPreviewModal() {
    const modal = new bootstrap.Modal(document.getElementById('libeiPreviewModal'));
    modal.show();

    // 加载李贝数据预览
    loadLibeiPreview();
}

// 加载李贝数据预览
function loadLibeiPreview() {
    const contentDiv = document.getElementById('libeiPreviewContent');

    // 显示加载状态
    contentDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在生成李贝数据预览...</p>
        </div>
    `;

    // 获取当前日期
    const currentDate = document.querySelector('[data-date]')?.getAttribute('data-date') ||
                       new Date().toISOString().split('T')[0];

    fetch('/api/generate_libei_preview', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: currentDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayLibeiPreview(data);
        } else {
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>预览失败：</strong>${data.error || '未知错误'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('加载李贝预览失败:', error);
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>网络错误：</strong>无法加载李贝数据预览
            </div>
        `;
    });
}

// 显示李贝数据预览
function displayLibeiPreview(data) {
    const contentDiv = document.getElementById('libeiPreviewContent');

    let html = `
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>数据日期：</strong>${data.date} |
                    <strong>特殊格式计算：</strong>当月日均故障数量 = 月累计 ÷ 当前天数
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6><i class="bi bi-table text-primary"></i> 普通报表数据</h6>
                <div class="table-responsive" style="max-height: 400px;">
                    <table class="table table-sm table-striped">
                        <thead class="table-primary">
                            <tr>
    `;

    // 普通报表表头
    data.normal_report.columns.forEach(col => {
        html += `<th class="text-nowrap">${col}</th>`;
    });
    html += `</tr></thead><tbody>`;

    // 普通报表数据
    data.normal_report.data.forEach(row => {
        html += '<tr>';
        data.normal_report.columns.forEach(col => {
            let value = row[col] || '';
            let cellClass = '';

            // 特殊样式
            if (col === '差值' && parseFloat(value) < 0) {
                cellClass = 'text-danger fw-bold';
            } else if (col === '时序' && parseFloat(String(value).replace('%', '')) > 100) {
                cellClass = 'text-danger fw-bold';
            } else if (row['地市_网格'] === '总计') {
                cellClass = 'fw-bold bg-warning bg-opacity-25';
            }

            html += `<td class="${cellClass}">${value}</td>`;
        });
        html += '</tr>';
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-6">
                <h6><i class="bi bi-heart text-success"></i> 特殊格式数据</h6>
                <div class="table-responsive" style="max-height: 400px;">
                    <table class="table table-sm table-striped">
                        <thead class="table-success">
                            <tr>
    `;

    // 李贝报表表头
    data.libei_report.columns.forEach(col => {
        let headerText = col;
        if (col === '当月日均故障数量') {
            headerText = '<span class="text-success fw-bold">当月日均故障数量</span>';
        }
        html += `<th class="text-nowrap">${headerText}</th>`;
    });
    html += `</tr></thead><tbody>`;

    // 李贝报表数据
    data.libei_report.data.forEach(row => {
        html += '<tr>';
        data.libei_report.columns.forEach(col => {
            let value = row[col] || '';
            let cellClass = '';

            // 特殊样式
            if (col === '当月日均故障数量') {
                cellClass = 'text-success fw-bold';
            } else if (col === '差值' && parseFloat(value) < 0) {
                cellClass = 'text-danger fw-bold';
            } else if (col === '时序' && parseFloat(String(value).replace('%', '')) > 100) {
                cellClass = 'text-danger fw-bold';
            } else if (row['地市_网格'] === '总计') {
                cellClass = 'fw-bold bg-warning bg-opacity-25';
            }

            html += `<td class="${cellClass}">${value}</td>`;
        });
        html += '</tr>';
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i>
                    <strong>对比说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>普通邮件：</strong>使用"当日退服数"，差值 = 当日目标 - 当日退服数</li>
                        <li><strong>李贝邮件：</strong>使用"当月日均故障数量"，差值 = 当日目标 - 当月日均故障数量</li>
                        <li><strong>计算公式：</strong>当月日均故障数量 = 月累计 ÷ 当前天数</li>
                    </ul>
                </div>
            </div>
        </div>
    `;

    contentDiv.innerHTML = html;
}

// 从预览界面发送李贝邮件
function sendLibeiEmailFromPreview() {
    // 关闭预览模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('libeiPreviewModal'));
    modal.hide();

    // 发送李贝邮件
    sendLibeiEmail();
}

// ==================== 快速推送操作函数 ====================

// 调试日志函数
function addDebugLog(message, type = 'info') {
    const logCard = document.getElementById('debugLogCard');
    const logContent = document.getElementById('debugLogContent');

    // 显示日志卡片
    logCard.style.display = 'block';

    // 获取当前时间
    const timestamp = new Date().toLocaleTimeString();

    // 根据类型设置颜色
    let color = '#d4d4d4'; // 默认白色
    let icon = '•';
    switch(type) {
        case 'error':
            color = '#f48771';
            icon = '❌';
            break;
        case 'success':
            color = '#a9dc76';
            icon = '✅';
            break;
        case 'warning':
            color = '#ffd866';
            icon = '⚠️';
            break;
        case 'info':
            color = '#78dce8';
            icon = 'ℹ️';
            break;
    }

    // 添加日志条目
    const logEntry = document.createElement('div');
    logEntry.style.color = color;
    logEntry.style.marginBottom = '4px';
    logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;

    logContent.appendChild(logEntry);

    // 自动滚动到底部
    logContent.scrollTop = logContent.scrollHeight;
}

function clearDebugLog() {
    document.getElementById('debugLogContent').innerHTML = '';
}

function hideDebugLog() {
    document.getElementById('debugLogCard').style.display = 'none';
}

function showDebugLog() {
    document.getElementById('debugLogCard').style.display = 'block';
    addDebugLog('🔍 调试日志已激活', 'info');
}

// 显示官方API发送模态框
function showOfficialApiModal() {
    addDebugLog('🚀 打开官方API发送模态框', 'info');

    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('officialApiDate').value = today;

    // 加载群组选项
    loadOfficialApiGroups();

    // 更新选项显示
    updateOfficialApiOptions();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('officialApiModal'));
    modal.show();
}

// 加载官方API群组选项
function loadOfficialApiGroups() {
    addDebugLog('📋 加载钉钉群组配置...', 'info');

    fetch('/api/get_dingtalk_apis')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('officialApiGroupSelector');
            if (data.apis && Object.keys(data.apis).length > 0) {
                let html = '';
                for (const [apiId, apiInfo] of Object.entries(data.apis)) {
                    if (apiInfo.enabled) {
                        const isChecked = apiInfo.is_current ? 'checked' : '';
                        html += `
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="officialApiChoice"
                                       id="officialApi_${apiId}" value="${apiId}" ${isChecked}>
                                <label class="form-check-label" for="officialApi_${apiId}">
                                    <i class="bi bi-chat-dots text-primary"></i> ${apiInfo.name}
                                    ${apiInfo.is_current ? '<span class="badge bg-primary ms-2">当前</span>' : ''}
                                    <small class="text-muted d-block">${apiInfo.description || '钉钉群组'}</small>
                                </label>
                            </div>
                        `;
                    }
                }
                container.innerHTML = html;
                addDebugLog('✅ 群组配置加载成功', 'success');
            } else {
                container.innerHTML = '<div class="text-muted">未找到可用的钉钉群组配置</div>';
                addDebugLog('⚠️ 未找到可用的群组配置', 'warning');
            }
        })
        .catch(error => {
            addDebugLog(`❌ 加载群组配置失败: ${error.message}`, 'error');
            console.error('加载钉钉群组选项失败:', error);
            document.getElementById('officialApiGroupSelector').innerHTML =
                '<div class="text-danger">加载群组配置失败</div>';
        });
}

// 更新官方API选项显示
function updateOfficialApiOptions() {
    const sendType = document.querySelector('input[name="sendType"]:checked').value;

    // 隐藏所有选项
    document.getElementById('excelOptions').style.display = 'none';
    document.getElementById('imageOptions').style.display = 'none';
    document.getElementById('cardOptions').style.display = 'none';

    // 显示对应选项
    if (sendType === 'excel') {
        document.getElementById('excelOptions').style.display = 'block';
    } else if (sendType === 'image') {
        document.getElementById('imageOptions').style.display = 'block';
    } else if (sendType === 'card') {
        document.getElementById('cardOptions').style.display = 'block';
    }
}

// 通过官方API发送
function sendViaOfficialApi() {
    addDebugLog('🚀 开始官方API发送流程', 'info');

    const sendType = document.querySelector('input[name="sendType"]:checked').value;
    const date = document.getElementById('officialApiDate').value;
    const selectedGroup = document.querySelector('input[name="officialApiChoice"]:checked');

    addDebugLog(`📋 发送类型: ${sendType}`, 'info');
    addDebugLog(`📅 报表日期: ${date}`, 'info');

    // 验证输入
    if (!date) {
        addDebugLog('❌ 请选择报表日期', 'error');
        showOfficialApiResult('warning', '请选择报表日期');
        return;
    }

    if (!selectedGroup) {
        addDebugLog('❌ 请选择目标群组', 'error');
        showOfficialApiResult('warning', '请选择目标群组');
        return;
    }

    addDebugLog(`📱 目标群组: ${selectedGroup.value}`, 'info');

    // 显示加载指示器
    const loadingIndicator = document.getElementById('officialApiLoadingIndicator');
    const resultContainer = document.getElementById('officialApiResultContainer');
    loadingIndicator.style.display = 'block';
    resultContainer.innerHTML = '';

    // 构建请求数据
    const requestData = {
        send_type: sendType,
        date: date,
        group_id: selectedGroup.value
    };

    // 添加特定选项
    if (sendType === 'excel') {
        requestData.include_charts = document.getElementById('includeCharts').checked;
    } else if (sendType === 'image') {
        requestData.include_summary = document.getElementById('includeSummary').checked;
    } else if (sendType === 'card') {
        requestData.card_title = document.getElementById('cardTitle').value;
        requestData.card_url = document.getElementById('cardUrl').value;
    }

    addDebugLog(`📤 发送请求数据: ${JSON.stringify(requestData)}`, 'info');

    // 发送请求
    fetch('/api/dingtalk_official_send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        addDebugLog(`📡 收到响应，状态码: ${response.status}`, 'info');
        return response.json();
    })
    .then(data => {
        loadingIndicator.style.display = 'none';
        addDebugLog(`📋 解析响应: ${JSON.stringify(data)}`, 'info');

        if (data.success) {
            addDebugLog('✅ 官方API发送成功！', 'success');
            showOfficialApiResult('success', '发送成功！', data.message, data.details);
        } else {
            addDebugLog(`❌ 官方API发送失败: ${data.error}`, 'error');
            showOfficialApiResult('danger', '发送失败', data.error);
        }
    })
    .catch(error => {
        loadingIndicator.style.display = 'none';
        addDebugLog(`🔥 网络请求失败: ${error.message}`, 'error');
        console.error('官方API发送失败:', error);
        showOfficialApiResult('danger', '请求失败', error.message);
    });
}

// 显示官方API结果
function showOfficialApiResult(type, title, message = '', details = null) {
    const resultContainer = document.getElementById('officialApiResultContainer');

    let detailsHtml = '';
    if (details) {
        if (typeof details === 'object') {
            detailsHtml = `
                <div class="mt-3">
                    <h6><i class="bi bi-info-circle"></i> 详细信息</h6>
                    <pre class="bg-light p-2 rounded small">${JSON.stringify(details, null, 2)}</pre>
                </div>
            `;
        } else {
            detailsHtml = `
                <div class="mt-2">
                    <small class="text-muted">${details}</small>
                </div>
            `;
        }
    }

    resultContainer.innerHTML = `
        <div class="alert alert-${type}" role="alert">
            <h6 class="alert-heading">
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${title}
            </h6>
            ${message ? `<p class="mb-0">${message}</p>` : ''}
            ${detailsHtml}
        </div>
    `;
}















// 发送可视化图表到钉钉
function sendVisualizationToDingtalk() {
    addDebugLog('📊 开始发送可视化图表到钉钉', 'info');

    const chartType = document.querySelector('input[name="chartType"]:checked').value;
    const dataRange = document.getElementById('dataRange').value;
    const includeTitle = document.getElementById('includeTitle').checked;
    const includeSummary = document.getElementById('includeDataSummary').checked;
    const includeTimestamp = document.getElementById('includeTimestamp').checked;
    const theme = document.getElementById('chartTheme').value;
    const quality = document.getElementById('imageQuality').value;
    const apiType = document.getElementById('apiType') ? document.getElementById('apiType').value : document.getElementById('globalApiType').value;

    addDebugLog(`📊 发送参数: 类型=${chartType}, 范围=${dataRange}, 主题=${theme}, API=${apiType}`, 'info');

    // 显示加载指示器
    const loadingIndicator = document.getElementById('visualizationLoadingIndicator');
    const resultContainer = document.getElementById('visualizationResultContainer');
    loadingIndicator.style.display = 'block';
    resultContainer.innerHTML = '';

    // 构建请求数据
    const requestData = {
        chart_type: chartType,
        data_range: dataRange,
        include_title: includeTitle,
        include_summary: includeSummary,
        include_timestamp: includeTimestamp,
        theme: theme,
        quality: quality,
        api_type: apiType
    };

    // 如果是自定义范围，添加日期
    if (dataRange === 'custom') {
        requestData.start_date = document.getElementById('startDate').value;
        requestData.end_date = document.getElementById('endDate').value;
    }

    addDebugLog(`📤 发送可视化请求: ${JSON.stringify(requestData)}`, 'info');

    // 发送请求
    fetch('/api/send_visualization', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        addDebugLog(`📡 收到可视化响应，状态码: ${response.status}`, 'info');
        return response.json();
    })
    .then(data => {
        loadingIndicator.style.display = 'none';
        addDebugLog(`📋 解析可视化响应: ${JSON.stringify(data)}`, 'info');

        if (data.success) {
            addDebugLog('✅ 可视化图表发送成功！', 'success');
            showVisualizationResult('success', '发送成功！', data.message, data.details);
        } else {
            addDebugLog(`❌ 可视化图表发送失败: ${data.error}`, 'error');
            showVisualizationResult('danger', '发送失败', data.error);
        }
    })
    .catch(error => {
        loadingIndicator.style.display = 'none';
        addDebugLog(`🔥 可视化网络请求失败: ${error.message}`, 'error');
        console.error('可视化发送失败:', error);
        showVisualizationResult('danger', '请求失败', error.message);
    });
}

// 显示可视化结果
function showVisualizationResult(type, title, message = '', details = null) {
    const resultContainer = document.getElementById('visualizationResultContainer');

    let detailsHtml = '';
    if (details) {
        if (typeof details === 'object') {
            detailsHtml = `
                <div class="mt-3">
                    <h6><i class="bi bi-info-circle"></i> 详细信息</h6>
                    <pre class="bg-light p-2 rounded small">${JSON.stringify(details, null, 2)}</pre>
                </div>
            `;
        } else {
            detailsHtml = `
                <div class="mt-2">
                    <small class="text-muted">${details}</small>
                </div>
            `;
        }
    }

    resultContainer.innerHTML = `
        <div class="alert alert-${type}" role="alert">
            <h6 class="alert-heading">
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${title}
            </h6>
            ${message ? `<p class="mb-0">${message}</p>` : ''}
            ${detailsHtml}
        </div>
    `;
}









// 发送图片报告
function sendImageReport() {
    addDebugLog('🖼️ 开始发送图片报告', 'info');

    // 获取当前日期
    const currentDate = document.querySelector('[data-date]')?.getAttribute('data-date') ||
                       new Date().toISOString().split('T')[0];

    addDebugLog(`📅 使用日期: ${currentDate}`, 'info');
    addDebugLog('📤 发送图片报告请求...', 'info');

    fetch('/api/dingtalk_official_send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            send_type: 'image',
            date: currentDate,
            include_summary: true
        })
    })
    .then(response => {
        addDebugLog(`📡 收到图片报告响应，状态码: ${response.status}`, 'info');
        return response.json();
    })
    .then(data => {
        addDebugLog(`📋 解析图片报告响应: ${JSON.stringify(data)}`, 'info');
        if (data.success) {
            addDebugLog('✅ 图片报告发送成功！', 'success');
            showAlert('图片报告发送成功！', 'success');
        } else {
            addDebugLog(`❌ 图片报告发送失败: ${data.error}`, 'error');
            showAlert(`图片报告发送失败：${data.error}`, 'error');
        }
    })
    .catch(error => {
        addDebugLog(`🔥 图片报告网络请求失败: ${error.message}`, 'error');
        console.error('图片报告发送失败:', error);
        showAlert('网络错误，图片报告发送失败', 'error');
    });
}

// 发送交互卡片
function sendActionCard() {
    addDebugLog('🎴 开始发送交互卡片', 'info');

    // 获取当前日期
    const currentDate = document.querySelector('[data-date]')?.getAttribute('data-date') ||
                       new Date().toISOString().split('T')[0];

    addDebugLog(`📅 使用日期: ${currentDate}`, 'info');
    addDebugLog('📤 发送交互卡片请求...', 'info');

    fetch('/api/dingtalk_official_send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            send_type: 'card',
            date: currentDate,
            card_title: '菏泽移网退服故障报表',
            card_url: window.location.href
        })
    })
    .then(response => {
        addDebugLog(`📡 收到交互卡片响应，状态码: ${response.status}`, 'info');
        return response.json();
    })
    .then(data => {
        addDebugLog(`📋 解析交互卡片响应: ${JSON.stringify(data)}`, 'info');
        if (data.success) {
            addDebugLog('✅ 交互卡片发送成功！', 'success');
            showAlert('交互卡片发送成功！', 'success');
        } else {
            addDebugLog(`❌ 交互卡片发送失败: ${data.error}`, 'error');
            showAlert(`交互卡片发送失败：${data.error}`, 'error');
        }
    })
    .catch(error => {
        addDebugLog(`🔥 交互卡片网络请求失败: ${error.message}`, 'error');
        console.error('交互卡片发送失败:', error);
        showAlert('网络错误，交互卡片发送失败', 'error');
    });
}

// 钉钉消息发送函数
function sendDingtalkMessage(type = 'text') {
    addDebugLog(`📱 钉钉消息发送类型: ${type}`, 'info');
    return new Promise((resolve, reject) => {
        addDebugLog('📤 调用钉钉发送功能...', 'info');
        // 普通文字消息，调用现有的钉钉发送功能
        sendToDingtalk('multiple_images')
            .then((result) => {
                addDebugLog('✅ 钉钉发送功能调用成功', 'success');
                resolve(result);
            })
            .catch((error) => {
                addDebugLog(`❌ 钉钉发送功能调用失败: ${error.message}`, 'error');
                reject(error);
            });
    });
}



// 快速发送钉钉消息
function sendQuickDingtalk() {
    addDebugLog('🚀 开始快速发送钉钉消息', 'info');
    console.log('🚀 快速发送钉钉消息');

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';

    addDebugLog('📤 准备发送钉钉消息...', 'info');

    // 调用钉钉发送函数
    sendDingtalkMessage('text')
        .then(() => {
            addDebugLog('✅ 钉钉消息发送成功', 'success');
        })
        .catch((error) => {
            addDebugLog(`❌ 钉钉消息发送失败: ${error.message}`, 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
}

// 切换到官方群组
function switchToOfficialGroup() {
    addDebugLog('🔄 切换到小测试群', 'info');

    fetch('/api/switch_to_official_group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('已切换到小测试群', 'success');
            // 更新当前群组显示
            updateCurrentDingtalkGroup('小测试群', 'official');
        } else {
            showAlert('切换群组失败', 'error');
        }
    })
    .catch(error => {
        console.error('切换群组失败:', error);
        showAlert('切换群组失败', 'error');
    });
}

// 显示钉钉群组选择模态框
function showDingtalkGroupModal() {
    // 加载钉钉群组列表并显示选择界面
    fetch('/api/get_dingtalk_groups')
        .then(response => response.json())
        .then(data => {
            if (data.success && Object.keys(data.groups).length > 0) {
                showDingtalkGroupSelector(data.groups, data.current);
            } else {
                showAlert('未找到可用的钉钉群组配置', 'warning');
            }
        })
        .catch(error => {
            console.error('加载钉钉群组失败:', error);
            showAlert('加载钉钉群组失败', 'error');
        });
}

// 显示钉钉API选择器
function showDingtalkApiSelector(apis, currentApi) {
    let html = `
        <div class="modal fade" id="dingtalkGroupModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-people"></i> 选择钉钉群组
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="list-group">
    `;

    Object.entries(apis).forEach(([apiId, config]) => {
        const isActive = apiId === currentApi;
        const statusClass = config.enabled ? 'success' : 'secondary';
        const statusText = config.enabled ? '已启用' : '未启用';
        const apiType = config.api_type || 'webhook';
        const typeIcon = apiType === 'official' ? '🏢' : '🔗';
        const typeText = apiType === 'official' ? '官方API' : 'Webhook';

        html += `
            <div class="list-group-item list-group-item-action ${isActive ? 'active' : ''}"
                 onclick="selectDingtalkGroup('${apiId}', '${config.name}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <i class="bi bi-${isActive ? 'check-circle-fill' : 'circle'}"></i>
                        ${config.name}
                        <small class="badge bg-info ms-2">${typeIcon} ${typeText}</small>
                    </h6>
                    <small class="badge bg-${statusClass}">${statusText}</small>
                </div>
                <p class="mb-1 text-muted small">群组ID: ${apiId}</p>
            </div>
        `;
    });

    html += `
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="showDingtalkConfigModal()">
                            <i class="bi bi-gear"></i> 管理群组
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('dingtalkGroupModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', html);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('dingtalkGroupModal'));
    modal.show();
}

// 选择钉钉群组
function selectDingtalkGroup(groupId, groupName) {
    // 切换到选择的群组
    fetch('/api/set_dingtalk_group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ group_id: groupId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 重新加载当前群组显示
            loadCurrentDingtalkGroup();

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('dingtalkGroupModal'));
            modal.hide();

            showAlert(`已切换到群组: ${groupName}`, 'success');
        } else {
            showAlert(`切换群组失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('切换群组失败:', error);
        showAlert('切换群组失败', 'error');
    });
}

// 显示钉钉配置模态框
function showDingtalkConfigModal() {
    // 创建钉钉配置模态框
    const modalHtml = `
        <div class="modal fade" id="dingtalkConfigModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-gear"></i> 钉钉群组配置
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-list"></i> 现有群组</h6>
                                <div id="existingGroups" class="border rounded p-3" style="min-height: 200px;">
                                    <div class="text-center text-muted">
                                        <div class="spinner-border spinner-border-sm" role="status"></div>
                                        <p class="mt-2">加载中...</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-plus-circle"></i> 添加新群组</h6>
                                <form id="addGroupForm">
                                    <div class="mb-3">
                                        <label for="groupName" class="form-label">群组名称</label>
                                        <input type="text" class="form-control" id="groupName" placeholder="例如：工作群">
                                    </div>
                                    <div class="mb-3">
                                        <label for="apiType" class="form-label">API类型</label>
                                        <select class="form-select" id="apiType" onchange="toggleApiFields()">
                                            <option value="webhook">Webhook方式</option>
                                            <option value="official">官方API方式</option>
                                        </select>
                                        <small class="text-muted">选择钉钉发送方式</small>
                                    </div>
                                    <div class="mb-3" id="webhookField">
                                        <label for="webhookUrl" class="form-label">Webhook URL</label>
                                        <textarea class="form-control" id="webhookUrl" rows="3"
                                                  placeholder="https://oapi.dingtalk.com/robot/send?access_token=..."></textarea>
                                        <small class="text-muted">从钉钉群机器人设置中获取</small>
                                    </div>
                                    <div class="mb-3" id="conversationField" style="display: none;">
                                        <label for="conversationId" class="form-label">群聊ID (openConversationId)</label>
                                        <input type="text" class="form-control" id="conversationId"
                                               placeholder="例如：cidk+3NJe7hGKglSgqEV0691g==">
                                        <small class="text-muted">从钉钉开发者后台获取群聊ID</small>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableGroup" checked>
                                            <label class="form-check-label" for="enableGroup">
                                                启用此群组
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-plus"></i> 添加群组
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-success" onclick="refreshDingtalkGroups()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新列表
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('dingtalkConfigModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('dingtalkConfigModal'));
    modal.show();

    // 加载现有群组
    loadExistingDingtalkGroups();

    // 绑定表单提交事件
    document.getElementById('addGroupForm').addEventListener('submit', handleAddDingtalkGroup);
}

// 加载现有钉钉群组
function loadExistingDingtalkGroups() {
    fetch('/api/get_all_dingtalk_apis')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('existingGroups');

            if (data.success && Object.keys(data.apis).length > 0) {
                let html = '';
                Object.entries(data.apis).forEach(([apiId, config]) => {
                    const isActive = apiId === data.current;
                    const statusClass = config.enabled ? 'success' : 'secondary';
                    const statusText = config.enabled ? '已启用' : '未启用';
                    const apiType = config.api_type || 'webhook';
                    const typeIcon = apiType === 'official' ? '🏢' : '🔗';
                    const typeText = apiType === 'official' ? '官方API' : 'Webhook';

                    html += `
                        <div class="card mb-2 ${isActive ? 'border-primary' : ''}">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">
                                            ${isActive ? '<i class="bi bi-check-circle-fill text-primary"></i>' : '<i class="bi bi-circle"></i>'}
                                            ${config.name}
                                            <small class="badge bg-info ms-2">${typeIcon} ${typeText}</small>
                                        </h6>
                                        <small class="text-muted">ID: ${apiId}</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-${statusClass} me-1">${statusText}</span>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteDingtalkGroup('${apiId}', '${config.name}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
            } else {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂无群组配置</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载群组失败:', error);
            document.getElementById('existingGroups').innerHTML = `
                <div class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p class="mt-2">加载失败</p>
                </div>
            `;
        });
}

// 切换API字段显示
function toggleApiFields() {
    const apiType = document.getElementById('apiType').value;
    const webhookField = document.getElementById('webhookField');
    const conversationField = document.getElementById('conversationField');

    if (apiType === 'official') {
        webhookField.style.display = 'none';
        conversationField.style.display = 'block';
    } else {
        webhookField.style.display = 'block';
        conversationField.style.display = 'none';
    }
}

// 处理添加钉钉群组
function handleAddDingtalkGroup(event) {
    event.preventDefault();

    const groupName = document.getElementById('groupName').value.trim();
    const apiType = document.getElementById('apiType').value;
    const webhookUrl = document.getElementById('webhookUrl').value.trim();
    const conversationId = document.getElementById('conversationId').value.trim();
    const enabled = document.getElementById('enableGroup').checked;

    if (!groupName) {
        showAlert('请填写群组名称', 'warning');
        return;
    }

    // 根据API类型验证必需字段
    if (apiType === 'webhook' && !webhookUrl) {
        showAlert('Webhook方式需要填写Webhook URL', 'warning');
        return;
    } else if (apiType === 'official' && !conversationId) {
        showAlert('官方API方式需要填写群聊ID', 'warning');
        return;
    }

    // 发送添加请求
    fetch('/api/add_dingtalk_api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: groupName,
            webhook: webhookUrl,
            enabled: enabled,
            api_type: apiType,
            conversation_id: conversationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`群组 "${groupName}" 添加成功`, 'success');

            // 清空表单
            document.getElementById('addGroupForm').reset();
            document.getElementById('enableGroup').checked = true;

            // 刷新群组列表
            loadExistingDingtalkGroups();
        } else {
            showAlert(`添加群组失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('添加群组失败:', error);
        showAlert('添加群组失败', 'error');
    });
}

// 删除钉钉群组
function deleteDingtalkGroup(apiId, groupName) {
    if (!confirm(`确定要删除群组 "${groupName}" 吗？`)) {
        return;
    }

    fetch('/api/delete_dingtalk_api', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ api_id: apiId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`群组 "${groupName}" 删除成功`, 'success');
            loadExistingDingtalkGroups();
        } else {
            showAlert(`删除群组失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('删除群组失败:', error);
        showAlert('删除群组失败', 'error');
    });
}

// 刷新钉钉群组列表
function refreshDingtalkGroups() {
    loadExistingDingtalkGroups();
    showAlert('群组列表已刷新', 'info');
}

// 加载当前钉钉群组显示
function loadCurrentDingtalkGroup() {
    fetch('/api/get_dingtalk_groups')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.current && data.groups[data.current]) {
                const currentGroup = data.groups[data.current];
                updateCurrentDingtalkGroup(currentGroup.name, 'official');
            } else {
                updateCurrentDingtalkGroup('未配置', 'unknown');
            }
        })
        .catch(error => {
            console.error('加载当前群组失败:', error);
            updateCurrentDingtalkGroup('加载失败', 'error');
        });
}

// 更新当前钉钉群组显示
function updateCurrentDingtalkGroup(groupName, apiType = 'webhook') {
    const groupElement = document.getElementById('currentDingtalkGroup');
    if (groupElement) {
        const typeIcon = apiType === 'official' ? '🏢' : '🔗';
        const typeText = apiType === 'official' ? '官方API' : 'Webhook';
        groupElement.innerHTML = `${typeIcon} ${groupName} <small class="text-muted">(${typeText})</small>`;
    }
}

// 显示操作提示
function showAlert(message, type = 'info') {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 更新上传方式提示
function updateUploadMethodHint() {
    const uploadMethod = document.getElementById('imageUploadMethod').value;
    const hintElement = document.getElementById('uploadMethodHint');

    if (uploadMethod === 'external') {
        hintElement.innerHTML = '🎉 支持文本图片混合';
        hintElement.className = 'text-success ms-1';
    } else if (uploadMethod === 'native') {
        hintElement.innerHTML = '📱 分别发送';
        hintElement.className = 'text-muted ms-1';
    } else {
        hintElement.innerHTML = '🤖 智能选择';
        hintElement.className = 'text-info ms-1';
    }
    hintElement.style.fontSize = '0.75rem';
}















// 页面加载时初始化快速操作区域
document.addEventListener('DOMContentLoaded', function() {
    // 检查邮件配置状态
    checkEmailConfigStatus();

    // 检查钉钉配置状态
    checkDingtalkConfigStatus();

    // 加载钉钉群组到下拉框
    loadDingtalkGroupsToSelect();

    // 初始化上传方式提示
    updateUploadMethodHint();
});

// 检查邮件配置状态
function checkEmailConfigStatus() {
    // 这里可以检查邮件配置是否完整
    // 暂时显示为已配置状态
    console.log('📧 邮件配置检查完成');
}

// 检查钉钉配置状态
function checkDingtalkConfigStatus() {
    // 这里可以检查钉钉配置是否完整
    // 暂时显示为已配置状态
    console.log('📱 钉钉配置检查完成');
}

// 加载钉钉群组列表到下拉框
function loadDingtalkGroupsToSelect() {
    fetch('/api/get_dingtalk_groups')
        .then(response => response.json())
        .then(data => {
            const selectElement = document.getElementById('dingtalkGroupSelect');
            if (!selectElement) return;

            // 清空现有选项
            selectElement.innerHTML = '';

            if (data.success && data.groups && Object.keys(data.groups).length > 0) {
                // 添加群组选项
                Object.entries(data.groups).forEach(([groupId, group]) => {
                    const option = document.createElement('option');
                    option.value = groupId;
                    const typeIcon = group.api_type === 'official' ? '🏢' : '🔗';
                    option.textContent = `${typeIcon} ${group.name}`;
                    option.selected = groupId === data.current;
                    selectElement.appendChild(option);
                });

                // 添加管理选项
                const divider = document.createElement('option');
                divider.disabled = true;
                divider.textContent = '──────────';
                selectElement.appendChild(divider);

                const manageOption = document.createElement('option');
                manageOption.value = 'manage';
                manageOption.textContent = '⚙️ 管理群组';
                selectElement.appendChild(manageOption);
            } else {
                // 没有群组时显示添加选项
                const addOption = document.createElement('option');
                addOption.value = 'add';
                addOption.textContent = '➕ 添加群组';
                selectElement.appendChild(addOption);
            }
        })
        .catch(error => {
            console.error('加载钉钉群组失败:', error);
            const selectElement = document.getElementById('dingtalkGroupSelect');
            if (selectElement) {
                selectElement.innerHTML = '<option value="">加载失败</option>';
            }
        });
}

// 显示添加群组模态框
function showAddGroupModal() {
    const modalHtml = `
        <div class="modal fade" id="addGroupModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-plus-circle text-success"></i> 添加钉钉群组
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addGroupForm">
                            <div class="mb-3">
                                <label for="groupName" class="form-label">
                                    <i class="bi bi-tag"></i> 群组名称
                                </label>
                                <input type="text" class="form-control" id="groupName"
                                       placeholder="例如：生产环境群" required>
                                <div class="form-text">给群组起一个便于识别的名称</div>
                            </div>
                            <div class="mb-3">
                                <label for="conversationId" class="form-label">
                                    <i class="bi bi-key"></i> 群组ID (openConversationId)
                                </label>
                                <input type="text" class="form-control" id="conversationId"
                                       placeholder="例如：cidk+3NJe7hGKglSgqEV0691g==" required>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <strong>获取方法：</strong><br>
                                        1. 在钉钉群中发送消息<br>
                                        2. 查看开发者工具或钉钉机器人配置<br>
                                        3. 复制 openConversationId 值
                                    </small>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" onclick="addNewGroup()">
                            <i class="bi bi-plus"></i> 添加群组
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('addGroupModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('addGroupModal'));
    modal.show();
}

// 添加新群组
function addNewGroup() {
    const groupName = document.getElementById('groupName').value.trim();
    const conversationId = document.getElementById('conversationId').value.trim();

    if (!groupName) {
        showAlert('请输入群组名称', 'warning');
        return;
    }

    if (!conversationId) {
        showAlert('请输入群组ID', 'warning');
        return;
    }

    // 发送添加请求
    fetch('/api/add_dingtalk_group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            group_name: groupName,
            conversation_id: conversationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addGroupModal'));
            modal.hide();
            // 重新加载群组列表
            setTimeout(() => loadDingtalkGroupsToSelect(), 500);
        } else {
            showAlert(`添加失败：${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('添加群组失败:', error);
        showAlert('网络错误，添加群组失败', 'error');
    });
}

// 切换钉钉群组
function switchDingtalkGroup() {
    const selectElement = document.getElementById('dingtalkGroupSelect');
    const selectedValue = selectElement.value;

    if (selectedValue === 'manage' || selectedValue === 'add') {
        // 显示群组管理界面
        showAddGroupModal();
        // 重新加载群组列表以恢复选择
        setTimeout(() => loadDingtalkGroupsToSelect(), 100);
        return;
    }

    if (!selectedValue) return;

    // 切换到选择的群组
    fetch('/api/set_dingtalk_group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            group_id: selectedValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addDebugLog(`✅ 已切换到群组: ${data.group_name}`, 'success');
            showAlert(`已切换到群组: ${data.group_name}`, 'success');
        } else {
            addDebugLog(`❌ 切换群组失败: ${data.error}`, 'error');
            showAlert('切换群组失败', 'error');
            // 重新加载群组列表
            loadDingtalkGroupsToSelect();
        }
    })
    .catch(error => {
        console.error('切换群组失败:', error);
        addDebugLog(`❌ 切换群组异常: ${error}`, 'error');
        showAlert('切换群组失败', 'error');
        // 重新加载群组列表
        loadDingtalkGroupsToSelect();
    });
}

</script>
{% endblock %}
