🔍 DEBUG: 从数据库查询时间段内退服次数
   时间范围: 2025-07-12 到 2025-08-10
   重复故障小区数: 10
🔍 DEBUG: 尝试连接数据库...
✅ DEBUG: 数据库连接成功
🔍 DEBUG: 开始查询各小区的时间段内退服次数
   牡丹区 - HZMD0207-ZX-S3H-(市区西联通8楼-牡丹区南关...: 14次
   单县 - HZSX0300-ZX-S2H-(单县中心机房-单县职业中专...: 5次
   单县 - HZSX0300-ZX-S2H-(单县中心机房-单县职业中专...: 5次
   单县 - HZSX0785-ZX-F2HS2W-(单县中心机房-单县职...: 5次
   牡丹区 - HZKF0262-ZX-F1H01-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZKF0262-ZX-F1H02-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZKF0262-ZX-F1H03-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 10次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 8次
   鲁西新区 - HZKF0259-ZX-S3H-(市区杨庄模块局-牡丹区冀鲁...: 8次
🔍 DEBUG: 数据库查询完成，各网格时间段内退服次数:
   单县: 15次
   牡丹区: 47次
   鲁西新区: 8次
   数据库查询总退服次数: 70
🔍 DEBUG: 分析模式判断
   analysis_mode = 'manual'
   analysis_mode == 'auto': False
   ✅ 使用时间范围分析逻辑: 查询数据库计算时间段内退服次数总和
   调用calculate_period_outages_from_db函数...
🔍 DEBUG: 从数据库查询时间段内退服次数
   时间范围: 2025-07-12 到 2025-08-10
   重复故障小区数: 10
🔍 DEBUG: 尝试连接数据库...
✅ DEBUG: 数据库连接成功
🔍 DEBUG: 开始查询各小区的时间段内退服次数
   牡丹区 - HZMD0207-ZX-S3H-(市区西联通8楼-牡丹区南关...: 14次
   单县 - HZSX0300-ZX-S2H-(单县中心机房-单县职业中专...: 5次
   单县 - HZSX0300-ZX-S2H-(单县中心机房-单县职业中专...: 5次
   单县 - HZSX0785-ZX-F2HS2W-(单县中心机房-单县职...: 5次
   牡丹区 - HZKF0262-ZX-F1H01-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZKF0262-ZX-F1H02-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZKF0262-ZX-F1H03-(康庄模块局-金色家园)...: 5次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 10次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 8次
   鲁西新区 - HZKF0259-ZX-S3H-(市区杨庄模块局-牡丹区冀鲁...: 8次
🔍 DEBUG: 数据库查询完成，各网格时间段内退服次数:
   单县: 15次
   牡丹区: 47次
   鲁西新区: 8次
   数据库查询总退服次数: 70
   函数返回结果: 3个网格
   总退服次数: 70
🔍 DEBUG: 从数据库查询时间段内退服次数
   时间范围: 2025-07-13 到 2025-08-11
   重复故障小区数: 6
🔍 DEBUG: 尝试连接数据库...
✅ DEBUG: 数据库连接成功
🔍 DEBUG: 开始查询各小区的时间段内退服次数
   东明县 - HZDM0278-ZX-S3H-(东明中心局-东明天正中央大...: 8次
   曹县 - HZCX0880-ZX-F9HU-(曹县安仁集支局-曹县安仁...: 10次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 9次
   东明县 - HZDM0278-ZX-S3H-(东明中心局-东明天正中央大...: 5次
   鲁西新区 - HZDM6089-ZX-F2H01-(恒盛模块局-东南郡E区...: 6次
   鲁西新区 - HZKF1249-ZX-S3H-(开发区恒盛模块局-鲁西新区...: 6次
🔍 DEBUG: 数据库查询完成，各网格时间段内退服次数:
   东明县: 13次
   曹县: 10次
   牡丹区: 9次
   鲁西新区: 12次
   数据库查询总退服次数: 44
🔍 DEBUG: 分析模式判断
   analysis_mode = 'manual'
   analysis_mode == 'auto': False
   ✅ 使用时间范围分析逻辑: 查询数据库计算时间段内退服次数总和
   调用calculate_period_outages_from_db函数...
🔍 DEBUG: 从数据库查询时间段内退服次数
   时间范围: 2025-07-13 到 2025-08-11
   重复故障小区数: 6
🔍 DEBUG: 尝试连接数据库...
✅ DEBUG: 数据库连接成功
🔍 DEBUG: 开始查询各小区的时间段内退服次数
   东明县 - HZDM0278-ZX-S3H-(东明中心局-东明天正中央大...: 8次
   曹县 - HZCX0880-ZX-F9HU-(曹县安仁集支局-曹县安仁...: 10次
   牡丹区 - HZMD0851-ZX-S3H-(市区付堤口模块局-牡丹区菏...: 9次
   东明县 - HZDM0278-ZX-S3H-(东明中心局-东明天正中央大...: 5次
   鲁西新区 - HZDM6089-ZX-F2H01-(恒盛模块局-东南郡E区...: 6次
   鲁西新区 - HZKF1249-ZX-S3H-(开发区恒盛模块局-鲁西新区...: 6次
🔍 DEBUG: 数据库查询完成，各网格时间段内退服次数:
   东明县: 13次
   曹县: 10次
   牡丹区: 9次
   鲁西新区: 12次
   数据库查询总退服次数: 44
   函数返回结果: 4个网格
   总退服次数: 44
