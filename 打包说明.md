# 菏泽数据提取工具 - 打包部署说明

## 方案一：PyInstaller打包成EXE（推荐）

### 步骤1：准备打包环境
```bash
# 安装依赖
pip install -r requirements.txt

# 如果遇到错误，可以逐个安装
pip install Flask pandas numpy openpyxl Pillow matplotlib requests Werkzeug pyinstaller
```

### 步骤2：创建打包脚本
运行以下命令进行打包：

```bash
pyinstaller --onefile --noconsole --add-data "templates;templates" --add-data "static;static" --hidden-import=openpyxl --hidden-import=PIL --hidden-import=matplotlib --name="菏泽数据提取工具" web_app.py
```

### 步骤3：打包参数说明
- `--onefile`: 打包成单个exe文件
- `--noconsole`: 不显示控制台窗口
- `--add-data`: 添加模板和静态文件
- `--hidden-import`: 添加隐式导入的模块
- `--name`: 设置exe文件名称

### 步骤4：运行打包后的程序
1. 在`dist`文件夹中找到生成的exe文件
2. 双击运行exe文件
3. 打开浏览器访问：http://127.0.0.1:5000

## 方案二：便携版Python部署

### 创建便携版目录结构
```
菏泽数据工具/
├── python/          # 便携版Python解释器
├── app/             # 应用文件
│   ├── web_app.py
│   ├── requirements.txt
│   └── ...
├── 启动工具.bat     # 启动脚本
└── 安装依赖.bat     # 依赖安装脚本
```

### 启动脚本示例（启动工具.bat）
```batch
@echo off
cd /d "%~dp0"
echo 正在启动菏泽数据提取工具...
python\python.exe app\web_app.py
pause
```

## 方案三：Docker容器化（适合服务器部署）

### Dockerfile示例
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

COPY . .

EXPOSE 5000

CMD ["python", "web_app.py"]
```

### 构建和运行
```bash
# 构建镜像
docker build -t heze-data-tool .

# 运行容器
docker run -p 5000:5000 heze-data-tool
```

## 使用建议

1. **个人电脑使用**：推荐方案一（PyInstaller打包）
2. **团队共享**：推荐方案二（便携版Python）
3. **服务器部署**：推荐方案三（Docker容器）

## 注意事项

1. 打包后的exe文件较大（约100-200MB），包含了完整的Python运行环境
2. 首次运行可能需要防火墙授权
3. 如果遇到杀毒软件误报，请添加到白名单
4. 确保目标电脑有足够的磁盘空间（至少500MB）

## 故障排除

### 常见问题及解决方案

**问题1：打包失败，提示模块找不到**
```bash
# 解决方案：手动指定隐式导入
pyinstaller --hidden-import=模块名 web_app.py
```

**问题2：exe运行时提示缺少文件**
```bash
# 解决方案：添加数据文件
pyinstaller --add-data "源路径;目标路径" web_app.py
```

**问题3：程序启动后无法访问**
- 检查防火墙设置
- 确认端口5000未被占用
- 尝试使用管理员权限运行 