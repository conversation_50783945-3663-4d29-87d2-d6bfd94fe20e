<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能数据分析 - 菏泽数据处理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 10px 30px;
        }
        .btn-outline-primary:hover {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: #667eea;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .analysis-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .analysis-card.selected {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .result-section {
            display: none;
        }
        .data-table {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-brain me-2"></i>智能数据分析</h3>
                        <p class="mb-0">通过累计数据推算单日数据，验证数据一致性</p>
                    </div>
                    <div class="card-body p-4">
                        <!-- 分析类型选择 -->
                        <div class="mb-4">
                            <h5><i class="fas fa-cogs me-2"></i>选择分析类型</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="analysis-card" data-type="daily_calculation">
                                        <div class="text-center">
                                            <i class="fas fa-calculator fa-2x mb-2"></i>
                                            <h6>单日数据计算</h6>
                                            <p class="small mb-0">从累计数据推算单日故障数据</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="analysis-card" data-type="data_validation">
                                        <div class="text-center">
                                            <i class="fas fa-check-double fa-2x mb-2"></i>
                                            <h6>数据一致性验证</h6>
                                            <p class="small mb-0">验证五张表的数据一致性</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="analysis-card" data-type="pyramid_analysis">
                                        <div class="text-center">
                                            <i class="fas fa-layer-group fa-2x mb-2"></i>
                                            <h6>数据金字塔分析</h6>
                                            <p class="small mb-0">验证明细→汇总→累计的关系</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="analysis-card" data-type="missing_data_detection">
                                        <div class="text-center">
                                            <i class="fas fa-search fa-2x mb-2"></i>
                                            <h6>缺失数据检测</h6>
                                            <p class="small mb-0">检测最近7天的数据缺失情况</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 参数设置 -->
                        <div class="mb-4">
                            <h5><i class="fas fa-sliders-h me-2"></i>分析参数</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="targetDate" class="form-label">目标日期</label>
                                        <input type="date" class="form-control" id="targetDate" required>
                                        <div class="form-text">选择要分析的日期</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">分析说明</label>
                                        <div class="form-control-plaintext" id="analysisDescription">
                                            请先选择分析类型
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="text-center mb-4">
                            <button type="button" class="btn btn-primary me-3" onclick="startAnalysis()">
                                <i class="fas fa-play me-2"></i>开始分析
                            </button>
                            <div class="btn-group me-3" role="group">
                                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-2"></i>智能导出
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportData('daily_from_cumulative')">
                                        <i class="fas fa-calculator me-2"></i>单日数据（从累计推算）
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportData('sector_summary')">
                                        <i class="fas fa-chart-bar me-2"></i>扇区数据汇总
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportData('comprehensive')">
                                        <i class="fas fa-table me-2"></i>综合对比报表
                                    </a></li>
                                </ul>
                            </div>
                            <a href="/" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>返回首页
                            </a>
                        </div>

                        <!-- 结果显示区域 -->
                        <div id="resultSection" class="result-section">
                            <h5><i class="fas fa-chart-line me-2"></i>分析结果</h5>
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>

                <!-- 数据金字塔结构说明 -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-layer-group me-2"></i>数据金字塔结构说明</h5>
                        <p class="mb-0 small">五张表构成完整的数据分析体系：从微观到宏观、从单日到累计</p>
                    </div>
                    <div class="card-body">
                        <!-- 金字塔可视化 -->
                        <div class="text-center mb-4">
                            <div style="font-family: monospace; line-height: 1.2;">
                                <div class="text-primary fw-bold">🏆 KPI考核总览板</div>
                                <div class="text-muted">↑ 直接取用</div>
                                <div class="text-info fw-bold">📊 累计故障摘要 (1-N日)</div>
                                <div class="text-muted">↑ 时间累加</div>
                                <div class="text-warning fw-bold">📋 日报故障摘要 (单日)</div>
                                <div class="text-muted">↑ 数据汇总</div>
                                <div class="text-success fw-bold">📝 原始故障日志 (明细)</div>
                            </div>
                        </div>

                        <!-- 详细说明 -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-file-alt me-2"></i>底层明细数据</h6>
                                <div class="small mb-3">
                                    <strong>扇区粒度退服故障统计清单-天粒度 (昨天)</strong><br>
                                    • 角色：故障原始日志<br>
                                    • 内容：每个故障小区的详细记录<br>
                                    • 粒度：最精细，每个小区一条记录
                                </div>

                                <h6><i class="fas fa-chart-bar me-2"></i>中层汇总数据</h6>
                                <div class="small mb-3">
                                    <strong>扇区粒度退服故障统计 (昨天)</strong><br>
                                    • 角色：故障日报摘要<br>
                                    • 内容：按地市和网格汇总的故障次数<br>
                                    • 关系：明细数据的加总结果
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar-week me-2"></i>累计统计数据</h6>
                                <div class="small mb-3">
                                    <strong>扇区粒度退服故障统计 (1日-昨天)</strong><br>
                                    • 角色：累计故障摘要<br>
                                    • 内容：月初到昨天的累计统计<br>
                                    • 关系：每日摘要的时间累加
                                </div>

                                <h6><i class="fas fa-trophy me-2"></i>顶层KPI数据</h6>
                                <div class="small mb-3">
                                    <strong>扇区粒度退服故障统计-月度</strong><br>
                                    • 角色：KPI考核总览板<br>
                                    • 内容：结合4G/5G扇区数的绩效评估<br>
                                    • 关系：直接取用累计摘要数据
                                </div>
                            </div>
                        </div>

                        <!-- 验证关系 -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-check-double me-2"></i>数据验证关系</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>关系A：明细 → 汇总</strong><br>
                                    <small>牡丹区日报摘要75次 = 明细日志中牡丹区所有小区故障次数之和</small>
                                </div>
                                <div class="col-md-6">
                                    <strong>关系B：汇总 → 累计</strong><br>
                                    <small>累计摘要数据 = 8月1日摘要 + 8月2日摘要 + ... + 昨天摘要</small>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>关系C：地市 = 网格之和</strong><br>
                                    <small>菏泽市总数1257 = 牡丹区275 + 巨野县206 + ... + 定陶区34</small>
                                </div>
                                <div class="col-md-6">
                                    <strong>关系D：累计 → KPI</strong><br>
                                    <small>KPI板中的故障数据直接来源于累计摘要，用于绩效计算</small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-success">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>核心公式：</strong>第N日单日数据 = 第N日累计数据 - 第N-1日累计数据<br>
                            <strong>效率提升：</strong>通过智能分析，从手动筛选15分钟 → 自动推算30秒
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedAnalysisType = null;

        // 页面加载时设置默认日期
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            document.getElementById('targetDate').value = yesterday.toISOString().split('T')[0];
        });

        // 分析类型选择
        document.querySelectorAll('.analysis-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除其他选中状态
                document.querySelectorAll('.analysis-card').forEach(c => c.classList.remove('selected'));
                
                // 添加选中状态
                this.classList.add('selected');
                
                // 记录选择的类型
                selectedAnalysisType = this.dataset.type;
                
                // 更新说明
                updateAnalysisDescription(selectedAnalysisType);
            });
        });

        // 更新分析说明
        function updateAnalysisDescription(type) {
            const descriptions = {
                'daily_calculation': '通过累计数据的差值计算单日故障数据，适用于从1-N日累计数据推算第N日的具体数据',
                'data_validation': '验证五张表（明细、汇总、累计、月度、KPI）之间的数据一致性，检查数据完整性和准确性',
                'pyramid_analysis': '验证数据金字塔结构：明细→汇总→累计→KPI的数据流动关系，确保每层数据的正确性',
                'missing_data_detection': '检测最近7天内各类数据的缺失情况，帮助识别需要补充的数据'
            };

            document.getElementById('analysisDescription').textContent = descriptions[type] || '请先选择分析类型';
        }

        // 开始分析
        function startAnalysis() {
            if (!selectedAnalysisType) {
                showAlert('请先选择分析类型', 'warning');
                return;
            }

            const targetDate = document.getElementById('targetDate').value;
            if (!targetDate) {
                showAlert('请选择目标日期', 'warning');
                return;
            }

            // 显示加载状态
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            
            resultSection.style.display = 'block';
            resultContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">正在分析中...</p></div>';

            // 发送分析请求
            fetch('/api/smart_data_analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: selectedAnalysisType,
                    target_date: targetDate
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data.data, selectedAnalysisType);
                } else {
                    showAlert(data.error, 'danger');
                    resultSection.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('分析失败:', error);
                showAlert('分析失败，请检查网络连接', 'danger');
                resultSection.style.display = 'none';
            });
        }

        // 显示分析结果
        function displayResults(data, analysisType) {
            const resultContent = document.getElementById('resultContent');
            let html = '';

            if (analysisType === 'daily_calculation') {
                html = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>单日数据计算完成</h6>
                        <p><strong>目标日期：</strong>${data.target_date}</p>
                        <p><strong>计算方法：</strong>${data.calculation_method}</p>
                        <p><strong>计算区县数：</strong>${data.districts_calculated}个</p>
                    </div>
                    <div class="data-table">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>区县</th>
                                    <th>单日故障次数</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(data.daily_data).map(([district, count]) => 
                                    `<tr><td>${district}</td><td>${count}</td></tr>`
                                ).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
            } else if (analysisType === 'data_validation') {
                html = `
                    <div class="alert ${data.is_consistent ? 'alert-success' : 'alert-warning'}">
                        <h6><i class="fas fa-${data.is_consistent ? 'check-circle' : 'exclamation-triangle'} me-2"></i>数据一致性验证结果</h6>
                        <p><strong>目标日期：</strong>${data.target_date}</p>
                        <p><strong>日度记录：</strong>${data.daily_records}条</p>
                        <p><strong>月度记录：</strong>${data.monthly_records}条</p>
                        <p><strong>扇区记录：</strong>${data.sector_records}条</p>
                        <p><strong>一致性状态：</strong>${data.is_consistent ? '✅ 数据一致' : '⚠️ 存在问题'}</p>
                    </div>
                    ${data.consistency_issues.length > 0 ? `
                        <div class="alert alert-warning">
                            <h6>发现的问题：</h6>
                            <ul class="mb-0">
                                ${data.consistency_issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                `;
            } else if (analysisType === 'pyramid_analysis') {
                const pyramidData = data;
                html = `
                    <div class="alert ${pyramidData.summary.data_flow_integrity ? 'alert-success' : 'alert-warning'}">
                        <h6><i class="fas fa-layer-group me-2"></i>数据金字塔分析结果</h6>
                        <p><strong>分析日期：</strong>${pyramidData.target_date}</p>
                        <p><strong>数据流完整性：</strong>${pyramidData.summary.data_flow_integrity ? '✅ 完整' : '⚠️ 存在问题'}</p>
                    </div>

                    <!-- 数据层级展示 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-database me-2"></i>数据层级结构</h6>
                                </div>
                                <div class="card-body">
                                    ${Object.entries(pyramidData.layers).map(([key, layer]) => `
                                        <div class="mb-2">
                                            <strong>${layer.name}:</strong> ${layer.record_count}条记录
                                            <br><small class="text-muted">${layer.description}</small>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>数据汇总统计</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>明细数据总计：</strong>${pyramidData.summary.total_detail_outages}次</p>
                                    <p><strong>日度数据总计：</strong>${pyramidData.summary.total_daily_outages}次</p>
                                    <p><strong>月度数据总计：</strong>${pyramidData.summary.total_monthly_outages}次</p>
                                    <p><strong>明细-日度匹配：</strong>${pyramidData.summary.detail_daily_match ? '✅ 一致' : '❌ 不一致'}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 关系验证结果 -->
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-link me-2"></i>数据关系验证</h6>
                        </div>
                        <div class="card-body">
                            ${Object.entries(pyramidData.relationships).map(([key, rel]) => `
                                <div class="mb-3">
                                    <h6>${rel.name} ${rel.is_consistent ? '✅' : '⚠️'}</h6>
                                    <p class="small text-muted">${rel.description}</p>
                                    ${rel.issues.length > 0 ? `
                                        <div class="alert alert-warning">
                                            <strong>发现问题：</strong>
                                            <ul class="mb-0">
                                                ${rel.issues.map(issue => `
                                                    <li>${JSON.stringify(issue)}</li>
                                                `).join('')}
                                            </ul>
                                        </div>
                                    ` : '<div class="text-success">✅ 数据关系正确</div>'}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (analysisType === 'missing_data_detection') {
                html = `
                    <div class="alert ${data.total_missing_days === 0 ? 'alert-success' : 'alert-warning'}">
                        <h6><i class="fas fa-search me-2"></i>缺失数据检测结果</h6>
                        <p><strong>检查期间：</strong>${data.check_period}</p>
                        <p><strong>缺失天数：</strong>${data.total_missing_days}天</p>
                    </div>
                    ${data.missing_data.length > 0 ? `
                        <div class="data-table">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>缺失类型</th>
                                        <th>日度</th>
                                        <th>月度</th>
                                        <th>扇区</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.missing_data.map(item => `
                                        <tr>
                                            <td>${item.date}</td>
                                            <td>${item.missing_types.join(', ')}</td>
                                            <td>${item.daily_count}</td>
                                            <td>${item.monthly_count}</td>
                                            <td>${item.sector_count}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : '<div class="alert alert-success">✅ 最近7天数据完整，无缺失</div>'}
                `;
            }

            resultContent.innerHTML = html;
        }

        // 智能导出数据
        function exportData(exportType) {
            const targetDate = document.getElementById('targetDate').value;
            if (!targetDate) {
                showAlert('请选择目标日期', 'warning');
                return;
            }

            // 显示导出状态
            showAlert('正在生成导出文件...', 'info');

            // 发送导出请求
            fetch('/api/export_optimized_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: exportType,
                    target_date: targetDate
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`导出成功！正在下载 ${data.filename}`, 'success');

                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    showAlert(data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                showAlert('导出失败，请检查网络连接', 'danger');
            });
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);

            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
