# 重复故障分析时间维度逻辑确认

## 📊 **当前系统逻辑确认**

### **重复次数定义（已确认）**
```python
# 代码实际逻辑
occurrence_count = len(history_df)  # 故障发生的天数
repeat_count = int(occurrence_count)  # 重复次数 = 故障天数

# 数据存储
sector_outage_data:
  date: '2025-06-25'        # 故障日期
  cell_name: '小区A'        # 小区名称
  outage_count: 3           # 该小区在该日的故障次数
```

**✅ 确认：重复次数 = 故障发生的天数（不是故障总次数）**

### **当前分析逻辑（已确认）**
```python
# 步骤1：查询目标日期的故障小区
target_cells = query_cells_on_date(target_date)

# 步骤2：对每个故障小区分析历史重复情况
for cell in target_cells:
    history = query_cell_history(cell, start_date, target_date)  # 包含目标日期
    if len(history) >= min_occurrences:
        # 认定为重复故障小区
```

**✅ 确认：以目标日期为基准，分析当天故障小区的历史重复情况**

## 🚀 **扩展时间维度方案**

### **推荐方案：每日独立判定（方案A）**

#### **本周分析实现**
```python
def analyze_week_repeated_outages(target_date, min_occurrences):
    """本周重复故障分析"""
    
    # 计算本周范围
    target_dt = datetime.strptime(target_date, '%Y-%m-%d')
    week_start = target_dt - timedelta(days=target_dt.weekday())  # 本周一
    
    print(f"本周分析范围: {week_start.strftime('%Y-%m-%d')} 到 {target_date}")
    
    all_repeated_outages = []
    daily_stats = {}
    
    # 分析本周每一天
    current_date = week_start
    while current_date <= target_dt:
        date_str = current_date.strftime('%Y-%m-%d')
        
        # 检查该日期是否有数据
        if has_data_on_date(date_str):
            # 使用现有的单日分析逻辑
            daily_repeated = analyze_single_day_repeated_outages(
                target_date=date_str,
                start_date=get_earliest_date(),  # 从最早日期开始
                min_occurrences=min_occurrences
            )
            
            daily_stats[date_str] = {
                'total_outages': count_daily_outages(date_str),
                'repeated_outages': len(daily_repeated),
                'repeated_cells': [r['cell_name'] for r in daily_repeated]
            }
            
            # 标记来源日期
            for outage in daily_repeated:
                outage['source_date'] = date_str
            
            all_repeated_outages.extend(daily_repeated)
        
        current_date += timedelta(days=1)
    
    # 合并去重（同一小区可能在多天都是重复故障）
    merged_outages = merge_repeated_outages(all_repeated_outages)
    
    return {
        'repeated_outages': merged_outages,
        'daily_stats': daily_stats,
        'analysis_info': {
            'mode': 'week',
            'start_date': week_start.strftime('%Y-%m-%d'),
            'end_date': target_date,
            'total_days': (target_dt - week_start).days + 1
        }
    }

def merge_repeated_outages(outages_list):
    """合并去重重复故障记录"""
    cell_map = {}
    
    for outage in outages_list:
        key = f"{outage['grid']}_{outage['cell_name']}"
        
        if key not in cell_map:
            cell_map[key] = outage.copy()
            cell_map[key]['source_dates'] = [outage['source_date']]
        else:
            # 更新统计信息（取最大值或累加）
            existing = cell_map[key]
            existing['repeat_count'] = max(existing['repeat_count'], outage['repeat_count'])
            existing['total_outages'] = max(existing['total_outages'], outage['total_outages'])
            existing['source_dates'].append(outage['source_date'])
            
            # 更新时间范围
            if outage['first_date'] < existing['first_date']:
                existing['first_date'] = outage['first_date']
            if outage['last_date'] > existing['last_date']:
                existing['last_date'] = outage['last_date']
    
    return list(cell_map.values())
```

#### **本月分析实现**
```python
def analyze_month_repeated_outages(target_date, min_occurrences):
    """本月重复故障分析"""
    
    # 计算本月范围
    target_dt = datetime.strptime(target_date, '%Y-%m-%d')
    month_start = target_dt.replace(day=1)  # 本月1日
    
    print(f"本月分析范围: {month_start.strftime('%Y-%m-%d')} 到 {target_date}")
    
    # 使用与本周分析相同的逻辑
    all_repeated_outages = []
    daily_stats = {}
    
    current_date = month_start
    while current_date <= target_dt:
        date_str = current_date.strftime('%Y-%m-%d')
        
        if has_data_on_date(date_str):
            daily_repeated = analyze_single_day_repeated_outages(
                target_date=date_str,
                start_date=get_earliest_date(),
                min_occurrences=min_occurrences
            )
            
            daily_stats[date_str] = {
                'total_outages': count_daily_outages(date_str),
                'repeated_outages': len(daily_repeated),
                'repeated_cells': [r['cell_name'] for r in daily_repeated]
            }
            
            for outage in daily_repeated:
                outage['source_date'] = date_str
            
            all_repeated_outages.extend(daily_repeated)
        
        current_date += timedelta(days=1)
    
    merged_outages = merge_repeated_outages(all_repeated_outages)
    
    return {
        'repeated_outages': merged_outages,
        'daily_stats': daily_stats,
        'analysis_info': {
            'mode': 'month',
            'start_date': month_start.strftime('%Y-%m-%d'),
            'end_date': target_date,
            'total_days': (target_dt - month_start).days + 1
        }
    }
```

### **时间边界确认**

#### **边界包含规则**
```python
# 本周分析
week_start = target_date - timedelta(days=target_date.weekday())  # 本周一
week_end = target_date  # 目标日期
# 范围：[本周一, 目标日期]，包含两端

# 本月分析  
month_start = target_date.replace(day=1)  # 本月1日
month_end = target_date  # 目标日期
# 范围：[本月1日, 目标日期]，包含两端

# 历史查询（对每个分析日期）
history_start = earliest_date  # 数据库最早日期
history_end = analysis_date    # 当前分析的日期
# 范围：[最早日期, 分析日期]，包含两端
```

#### **示例说明**
```
假设今天是2025-06-25（周三）

本周分析：
- 分析范围：2025-06-23（周一）到 2025-06-25（周三）
- 对每一天（23日、24日、25日）分别进行重复故障分析
- 每天的历史查询都是从数据库最早日期到该天

本月分析：
- 分析范围：2025-06-01（月初）到 2025-06-25（今天）
- 对每一天（1日到25日）分别进行重复故障分析
- 每天的历史查询都是从数据库最早日期到该天
```

## 📋 **Excel导出扩展**

### **标题和信息行**
```python
def generate_extended_excel_title(analysis_info):
    """生成扩展的Excel标题"""
    mode_names = {
        'auto': '智能分析（全历史）',
        'week': '本周分析',
        'month': '本月分析',
        'manual': '自定义分析'
    }
    
    mode_name = mode_names.get(analysis_info['mode'], '未知模式')
    return f"重复故障分析报告 - {analysis_info['end_date']} ({mode_name})"

def generate_extended_excel_info(analysis_info):
    """生成扩展的Excel信息行"""
    if analysis_info['mode'] in ['week', 'month']:
        return f"分析范围: {analysis_info['start_date']} 至 {analysis_info['end_date']} " \
               f"(共{analysis_info['total_days']}天) | " \
               f"分析模式: {mode_names[analysis_info['mode']]} | " \
               f"最小重复: {analysis_info['min_occurrences']}次"
    else:
        # 保持原有格式
        return generate_original_excel_info(analysis_info)
```

### **区县通报扩展**
```python
def generate_extended_report_text(analysis_result):
    """生成扩展的区县情况通报"""
    mode = analysis_result['analysis_info']['mode']
    
    if mode == 'week':
        period_desc = "本周"
    elif mode == 'month':
        period_desc = "本月"
    else:
        period_desc = "本次分析"
    
    # 基础通报
    report_parts = [
        f"{period_desc}共涉及{total_grids}个区县，发现{total_outages}个重复故障小区。"
    ]
    
    # 如果是周/月分析，添加时间维度信息
    if mode in ['week', 'month']:
        daily_stats = analysis_result.get('daily_stats', {})
        active_days = len([d for d, s in daily_stats.items() if s['total_outages'] > 0])
        
        report_parts.append(
            f"{period_desc}期间共{active_days}天有故障数据。"
        )
    
    # 继续原有的通报逻辑...
    return " ".join(report_parts)
```

## 🎯 **实施建议**

### **第一阶段：核心功能**
1. ✅ 实现本周分析模式
2. ✅ 实现本月分析模式  
3. ✅ 扩展前端界面选择器
4. ✅ 修改后端API处理逻辑

### **第二阶段：用户体验**
1. ✅ 添加时间范围预览
2. ✅ 优化Excel导出格式
3. ✅ 扩展邮件发送功能
4. ✅ 添加每日统计信息

### **第三阶段：高级功能**
1. ✅ 支持自定义日期范围
2. ✅ 添加周/月对比分析
3. ✅ 集成趋势图表
4. ✅ 支持多时间段导出

## ✅ **逻辑确认总结**

### **当前系统逻辑（已确认正确）**
- ✅ 重复次数 = 故障发生的天数
- ✅ 以目标日期为基准分析
- ✅ 包含目标日期在历史查询中
- ✅ 只分析目标日期当天有故障的小区

### **扩展方案（推荐方案A）**
- ✅ 本周/本月分析：每日独立判定后合并
- ✅ 保持与现有逻辑的一致性
- ✅ 时间边界包含两端
- ✅ 支持灵活的时间维度扩展

这个方案既保持了逻辑一致性，又提供了灵活的时间维度分析能力！🎉
