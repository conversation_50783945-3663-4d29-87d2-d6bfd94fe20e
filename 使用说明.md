# 菏泽数据提取工具使用说明

## 功能介绍

这个工具可以自动从扇区粒度退服故障统计表中提取菏泽相关数据，支持日度和月度数据的处理。

## 文件说明

1. **菏泽数据提取工具.py** - 基础版本
2. **菏泽数据提取工具_增强版.py** - 增强版本（推荐使用）
3. **提取菏泽数据.bat** - 一键运行批处理文件
4. **使用说明.md** - 本说明文档

## 使用方法

### 方法一：双击批处理文件（推荐）

1. 将Excel文件放在同一目录下
2. 双击 `提取菏泽数据.bat` 文件
3. 等待处理完成，查看生成的结果文件

### 方法二：命令行运行

```bash
# 自动查找文件并处理
python 菏泽数据提取工具_增强版.py --auto

# 指定文件路径
python 菏泽数据提取工具_增强版.py --daily "扇区粒度退服故障统计.xlsx" --monthly "扇区粒度退服故障统计-月度.xlsx"

# 指定输出目录
python 菏泽数据提取工具_增强版.py --auto --output "./结果/"
```

## 输入文件要求

### 日度文件
- 文件名包含 "扇区粒度退服故障统计"
- 不包含 "月度" 关键词
- 包含菏泽各区县的退服数据

### 月度文件
- 文件名包含 "月度" 关键词
- 包含菏泽各区县的月累计数据

## 输出结果

工具会生成一个Excel文件，包含两个工作表：

### 日度数据表
| 地市 | 网格 | 总退服次数 |
|------|------|------------|
| 菏泽 | 牡丹区 | 121 |
| 菏泽 | 东明县 | 54 |
| ... | ... | ... |
| 菏泽 | 总计 | 385 |

### 月度数据表
| 序号 | 地市 | 地市_网格 | 月累计退服总数 |
|------|------|-----------|----------------|
| 1 | 菏泽 | 菏泽_牡丹区 | 380 |
| 2 | 菏泽 | 菏泽_东明县 | 227 |
| ... | ... | ... | ... |
| 11 | 菏泽 | 总计 | 1479 |

## 特点

1. **自动识别文件** - 无需手动指定文件路径
2. **数据验证** - 自动验证数据完整性
3. **智能校正** - 自动校正月度数据与扇区故障次数的差异
4. **排序功能** - 按退服次数降序排列
5. **总计计算** - 自动计算总计数据
6. **错误处理** - 友好的错误提示
7. **批量处理** - 支持同时处理多个文件

## 数据校正功能

工具会自动检查月度数据的一致性：

1. **提取扇区故障次数总计** - 从菏泽总计行获取标准数值
2. **计算各区县总和** - 统计所有区县数据的总和
3. **自动校正差异** - 如果存在差值，自动补到鲁西新区
4. **输出校正信息** - 显示校正前后的数据变化

### 校正示例
```
⚠ 数据不一致：扇区故障次数总计(1573) vs 各区县总和(1479)，差值: 94
✓ 已将差值 94 补到鲁西新区：55 → 149
```

## 注意事项

1. 确保Excel文件格式正确
2. 文件名需要包含关键词以便自动识别
3. 如果数据格式发生变化，可能需要调整脚本
4. 建议每次使用前备份原始文件

## 常见问题

### Q: 提示找不到文件怎么办？
A: 检查文件名是否包含关键词，或使用 `--daily` 和 `--monthly` 参数手动指定文件路径。

### Q: 提取的数据不完整怎么办？
A: 检查Excel文件格式是否正确，确保菏泽数据在预期的位置。

### Q: 如何处理多个文件？
A: 将所有文件放在同一目录下，工具会自动选择最新的文件进行处理。

## 技术支持

如果遇到问题，请检查：
1. Python环境是否正确安装
2. pandas和openpyxl库是否已安装
3. Excel文件格式是否正确
4. 文件路径是否正确

## 更新日志

- v1.0: 基础功能实现
- v2.0: 增加自动文件识别和错误处理
- v2.1: 优化数据提取逻辑，增加批处理支持
