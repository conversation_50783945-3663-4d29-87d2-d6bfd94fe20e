{% extends "base.html" %}

{% block title %}告警详情 - {{ alarm.code_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>📋 告警详情</h5>
                <div>
                    <span class="badge {% if alarm.is_baseline and alarm.is_active %}bg-primary{% elif alarm.is_new and alarm.is_active %}bg-warning{% elif not alarm.is_new and alarm.is_active %}bg-info{% else %}bg-secondary{% endif %}">
                        {% if alarm.is_baseline and alarm.is_active %}📊 基线
                        {% elif alarm.is_new and alarm.is_active %}🆕 新的
                        {% elif not alarm.is_new and alarm.is_active %}📍 持续
                        {% else %}❌ 已清除{% endif %}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <!-- 基本信息 -->
                <h6 class="text-primary">🔍 基本信息</h6>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-sm table-bordered">
                            <tr>
                                <th width="30%">告警名称 <small class="text-muted">(code_name)</small></th>
                                <td><strong>{{ alarm.code_name or '未知' }}</strong></td>
                            </tr>
                            <tr>
                                <th>告警级别 <small class="text-muted">(perceived_severity_name)</small></th>
                                <td><span class="severity-{{ (alarm.perceived_severity_name or '').lower() }}"><strong>{{ alarm.perceived_severity_name or '未知' }}</strong></span></td>
                            </tr>
                            <tr>
                                <th>确认状态 <small class="text-muted">(ack_state_name)</small></th>
                                <td><span class="badge {% if alarm.ack_state_name == '已确认' %}bg-success{% else %}bg-warning{% endif %}">{{ alarm.ack_state_name or '未确认' }}</span></td>
                            </tr>
                            <tr>
                                <th>告警类型 <small class="text-muted">(alarm_type_name)</small></th>
                                <td>{{ alarm.alarm_type_name or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>告警代码 <small class="text-muted">(alarm_code)</small></th>
                                <td><code>{{ alarm.alarm_code or '未知' }}</code></td>
                            </tr>
                            <tr>
                                <th>原因名称 <small class="text-muted">(reason_name)</small></th>
                                <td>{{ alarm.reason_name or '未知' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-bordered">
                            <tr>
                                <th width="30%">网元名称 <small class="text-muted">(me_name)</small></th>
                                <td>{{ alarm.me_name or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>IP地址 <small class="text-muted">(ne_ip)</small></th>
                                <td><code>{{ alarm.ne_ip or '未知' }}</code></td>
                            </tr>
                            <tr>
                                <th>资源类型 <small class="text-muted">(res_type_name)</small></th>
                                <td>{{ alarm.res_type_name or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>位置信息 <small class="text-muted">(position_name)</small></th>
                                <td>{{ alarm.position_name or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>清除状态 <small class="text-muted">(clear_state_name)</small></th>
                                <td>{{ alarm.clear_state_name or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>确认用户 <small class="text-muted">(ack_user_id)</small></th>
                                <td>{{ alarm.ack_user_id or '无' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 时间信息 -->
                <h6 class="text-primary">⏰ 时间信息</h6>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <table class="table table-sm table-bordered">
                            <tr>
                                <th width="15%">发生时间 <small class="text-muted">(alarm_raised_time)</small></th>
                                <td width="20%">{{ alarm.alarm_raised_time_formatted }}</td>
                                <th width="15%">考核持续时间 <small class="text-muted">(effective_duration)</small></th>
                                <td width="20%"><strong class="text-danger">{{ alarm.duration_text }}</strong></td>
                                <th width="15%">首次发现 <small class="text-muted">(first_seen_at)</small></th>
                                <td>{{ alarm.first_seen_at_formatted or '未知' }}</td>
                            </tr>
                            <tr>
                                <th>最后发现 <small class="text-muted">(last_seen_at)</small></th>
                                <td>{{ alarm.last_seen_at_formatted or '未知' }}</td>
                                <th>状态变更 <small class="text-muted">(status_changed_at)</small></th>
                                <td>{{ alarm.status_changed_at_formatted or '未知' }}</td>
                                <th>创建时间 <small class="text-muted">(created_at)</small></th>
                                <td>{{ alarm.created_at_formatted or '未知' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 详细描述 -->
                {% if alarm.additional_text %}
                <h6 class="text-primary">📝 详细描述</h6>
                <div class="alert alert-info mb-4">
                    <pre style="white-space: pre-wrap; margin: 0;">{{ alarm.additional_text }}</pre>
                </div>
                {% endif %}

                {% if alarm.comment_text %}
                <h6 class="text-primary">💬 备注信息</h6>
                <div class="alert alert-secondary mb-4">
                    <pre style="white-space: pre-wrap; margin: 0;">{{ alarm.comment_text }}</pre>
                </div>
                {% endif %}

                <!-- 原始数据 -->
                {% if alarm.raw_data_parsed %}
                <h6 class="text-primary">🔧 完整原始数据 <small class="text-muted">(中英文对照)</small></h6>
                <div class="accordion mb-4" id="rawDataAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rawDataCollapse">
                                点击展开/收起原始数据 ({{ alarm.raw_data_parsed|length }} 个字段)
                            </button>
                        </h2>
                        <div id="rawDataCollapse" class="accordion-collapse collapse" data-bs-parent="#rawDataAccordion">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="20%">字段名 (英文)</th>
                                                <th width="20%">中文说明</th>
                                                <th width="60%">值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for key, value in alarm.raw_data_parsed.items() %}
                                            <tr>
                                                <td><code>{{ key }}</code></td>
                                                <td><span class="text-info fw-bold">{{ alarm.field_translations.get(key.lower(), key) }}</span></td>
                                                <td>
                                                    {% if value is mapping %}
                                                        <details>
                                                            <summary class="text-primary" style="cursor: pointer;">展开对象 ({{ value|length }} 个属性)</summary>
                                                            <pre class="mt-2 p-2 bg-light border rounded" style="font-size: 12px;">{{ value | tojson(indent=2) }}</pre>
                                                        </details>
                                                    {% elif value is string and value|length > 100 %}
                                                        <details>
                                                            <summary class="text-primary" style="cursor: pointer;">{{ value[:100] }}... (点击查看完整内容)</summary>
                                                            <pre class="mt-2 p-2 bg-light border rounded" style="font-size: 12px; white-space: pre-wrap;">{{ value }}</pre>
                                                        </details>
                                                    {% else %}
                                                        <span>{{ value }}</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 操作按钮 -->
                <div class="d-flex gap-2">
                    <a href="javascript:history.back()" class="btn btn-secondary">← 返回列表</a>
                    <a href="/" class="btn btn-outline-primary">🏠 首页</a>
                    <button onclick="window.print()" class="btn btn-outline-info">🖨️ 打印</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.severity-critical { color: #dc3545; font-weight: bold; }
.severity-major { color: #fd7e14; font-weight: bold; }
.severity-minor { color: #ffc107; font-weight: bold; }
.severity-warning { color: #20c997; font-weight: bold; }
.severity-cleared { color: #6c757d; }
</style>
{% endblock %}