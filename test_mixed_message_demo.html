<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉文本图片混合消息演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .demo-card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .comparison-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .success-card {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .warning-card {
            border-color: #ffc107;
            background-color: #fffdf5;
        }
        .message-preview {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 10px 10px 0;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="demo-card">
                    <div class="demo-header text-center">
                        <h1 class="display-6 mb-3">
                            <i class="bi bi-chat-heart"></i>
                            钉钉文本图片混合消息功能
                        </h1>
                        <p class="lead mb-0">🎉 重大突破！成功实现文本和图片在一个消息框中显示</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能亮点 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="feature-highlight text-center">
                    <h3><i class="bi bi-star-fill"></i> 核心突破</h3>
                    <p class="mb-0">通过钉钉官方API的 <code>sampleLink</code> 消息类型，实现了真正的文本图片混合显示！</p>
                </div>
            </div>
        </div>

        <!-- 对比展示 -->
        <div class="row">
            <div class="col-md-6">
                <div class="comparison-card warning-card">
                    <h5 class="text-warning">
                        <i class="bi bi-exclamation-triangle"></i> 改进前（钉钉原生上传）
                    </h5>
                    <div class="message-preview">
                        <strong>消息1：</strong><br>
                        📈 当日退服总数：454 次<br>
                        🎯 当日目标：311 次<br>
                        📈 截止7月6日，牡丹区、曹县...
                    </div>
                    <div class="message-preview">
                        <strong>消息2：</strong><br>
                        [图片：综合报表]
                    </div>
                    <div class="message-preview">
                        <strong>消息3：</strong><br>
                        [图片：扇区数据]
                    </div>
                    <div class="message-preview">
                        <strong>消息4：</strong><br>
                        [图片：趋势分析图]
                    </div>
                    <p class="text-muted mt-3">
                        <i class="bi bi-x-circle text-danger"></i> 
                        消息分散，阅读不连贯，需要滚动查看
                    </p>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="comparison-card success-card">
                    <h5 class="text-success">
                        <i class="bi bi-check-circle"></i> 改进后（外部图床）
                    </h5>
                    <div class="message-preview border border-success">
                        <strong>📱 菏泽移网退服故障报表 - 2025-07-06</strong><br><br>
                        📈 当日退服总数：454 次<br>
                        🎯 当日目标：311 次<br>
                        📈 截止7月6日，牡丹区、曹县...<br><br>
                        <div class="text-center">
                            <img src="https://via.placeholder.com/200x120/28a745/ffffff?text=综合报表" 
                                 class="img-fluid rounded" alt="综合报表">
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">🔗 点击查看详情</small>
                        </div>
                    </div>
                    <div class="message-preview">
                        <strong>消息2：</strong><br>
                        [图片：扇区数据]
                    </div>
                    <div class="message-preview">
                        <strong>消息3：</strong><br>
                        [图片：趋势分析图]
                    </div>
                    <p class="text-success mt-3">
                        <i class="bi bi-check-circle text-success"></i> 
                        图文并茂，阅读连贯，专业美观
                    </p>
                </div>
            </div>
        </div>

        <!-- 技术实现 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 技术实现原理
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-1-circle text-primary"></i> 发现关键API</h6>
                                <p>通过深入研究钉钉官方文档，发现了 <code>sampleLink</code> 消息类型支持同时包含文本和图片。</p>
                                
                                <h6><i class="bi bi-2-circle text-primary"></i> 智能回退机制</h6>
                                <p>优先尝试链接消息，失败时自动回退到传统的分别发送方式，确保可靠性。</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-3-circle text-primary"></i> 用户选择权</h6>
                                <p>在前端提供上传方式选择，用户可以根据需要选择外部图床或钉钉原生上传。</p>
                                
                                <h6><i class="bi bi-4-circle text-primary"></i> 完美兼容</h6>
                                <p>不影响现有功能，钉钉原生上传继续使用传统方式，外部图床享受混合消息。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用指南 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-book"></i> 使用指南
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">
                                <i class="bi bi-lightbulb"></i> 如何使用文本图片混合消息？
                            </h6>
                            <ol class="mb-0">
                                <li>在钉钉发送控制区域，找到 <strong>"上传"</strong> 选项</li>
                                <li>选择 <strong>"外部图床"</strong> 选项（会显示 🎉 支持文本图片混合）</li>
                                <li>正常发送钉钉消息，第一张图片将与文本合并显示</li>
                                <li>剩余图片继续以单独消息发送</li>
                            </ol>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="bi bi-cloud-upload text-info" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">外部图床</h6>
                                    <small class="text-success">🎉 支持文本图片混合</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="bi bi-phone text-primary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">钉钉原生</h6>
                                    <small class="text-muted">📱 分别发送</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="bi bi-cpu text-warning" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2">智能上传</h6>
                                    <small class="text-info">🤖 智能选择</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-clipboard-check"></i> 测试验证结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ 测试通过的消息类型</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>纯文本消息 (sampleText)</span>
                                        <span class="badge bg-success">✅ 成功</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>链接消息 (sampleLink)</span>
                                        <span class="badge bg-success">✅ 成功</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>纯图片消息 (sampleImageMsg)</span>
                                        <span class="badge bg-success">✅ 成功</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Markdown消息 (sampleMarkdown)</span>
                                        <span class="badge bg-success">✅ 成功</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>📊 测试环境信息</h6>
                                <ul class="list-unstyled">
                                    <li><strong>测试群组：</strong> 小测试群</li>
                                    <li><strong>测试时间：</strong> 2025-07-08 11:50:28</li>
                                    <li><strong>成功率：</strong> 100% (4/4)</li>
                                    <li><strong>关键发现：</strong> 链接消息可实现文本图片混合！</li>
                                </ul>
                                
                                <div class="alert alert-info mt-3">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>重要发现：</strong>钉钉官方API的链接消息类型完美解决了文本图片混合的需求！
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="row mt-4 mb-4">
            <div class="col-12 text-center">
                <a href="/" class="btn btn-primary btn-lg">
                    <i class="bi bi-house"></i> 返回主页面体验新功能
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
