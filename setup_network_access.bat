@echo off
echo 🌐 设置网络访问权限...
echo.

echo 📡 获取当前IP地址:
ipconfig | findstr "IPv4"
echo.

echo 🔥 添加防火墙规则（允许端口5000）:
netsh advfirewall firewall add rule name="Python Web App Port 5000" dir=in action=allow protocol=TCP localport=5000
echo.

echo ✅ 网络访问设置完成！
echo.
echo 🌐 其他电脑现在可以通过以下地址访问:
echo    http://你的IP地址:5000
echo.
echo 💡 如果仍无法访问，请检查:
echo    1. 确保应用正在运行
echo    2. 检查网络连接
echo    3. 确认防火墙设置
echo.
pause
