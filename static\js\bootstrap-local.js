/* 本地Bootstrap JavaScript - 离线版本 */

// 简化的Modal功能
class Modal {
    constructor(element) {
        this.element = element;
        this.backdrop = null;
    }

    show() {
        // 创建背景遮罩
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'modal-backdrop fade show';
        this.backdrop.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1040;
            width: 100vw;
            height: 100vh;
            background-color: #000;
            opacity: 0.5;
        `;
        document.body.appendChild(this.backdrop);

        // 显示模态框
        this.element.style.display = 'block';
        this.element.style.cssText += `
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1050;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
        `;
        
        // 添加body样式
        document.body.style.overflow = 'hidden';
        document.body.style.paddingRight = '17px';

        // 绑定关闭事件
        this.bindCloseEvents();
    }

    hide() {
        // 隐藏模态框
        this.element.style.display = 'none';
        
        // 移除背景遮罩
        if (this.backdrop) {
            document.body.removeChild(this.backdrop);
            this.backdrop = null;
        }
        
        // 恢复body样式
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    bindCloseEvents() {
        // 点击背景关闭
        if (this.backdrop) {
            this.backdrop.addEventListener('click', () => {
                this.hide();
            });
        }

        // 点击关闭按钮
        const closeButtons = this.element.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.hide();
            });
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hide();
            }
        });
    }

    static getInstance(element) {
        if (!element._modalInstance) {
            element._modalInstance = new Modal(element);
        }
        return element._modalInstance;
    }
}

// 全局bootstrap对象
window.bootstrap = {
    Modal: Modal
};

// 简化的XLSX功能（如果没有网络）
if (typeof XLSX === 'undefined') {
    window.XLSX = {
        utils: {
            book_new: function() {
                return { SheetNames: [], Sheets: {} };
            },
            aoa_to_sheet: function(data) {
                return { '!ref': 'A1:Z100', data: data };
            },
            book_append_sheet: function(wb, ws, name) {
                wb.SheetNames.push(name);
                wb.Sheets[name] = ws;
            }
        },
        writeFile: function(wb, filename) {
            // 简化版本：提示用户手动保存
            alert('离线模式下无法自动下载文件，请手动复制数据保存。');
        }
    };
}

// 离线检测
function checkOfflineMode() {
    // 检测是否为离线模式
    var isOffline = !navigator.onLine;

    // 检测CDN资源是否加载失败
    var cdnFailed = false;
    try {
        // 检查Bootstrap是否从CDN加载
        var links = document.querySelectorAll('link[href*="cdn.jsdelivr.net"]');
        links.forEach(function(link) {
            if (link.sheet === null) {
                cdnFailed = true;
            }
        });
    } catch (e) {
        cdnFailed = true;
    }

    if (isOffline || cdnFailed) {
        showOfflineNotice();
    }
}

function showOfflineNotice() {
    var notice = document.createElement('div');
    notice.className = 'offline-notice';
    notice.innerHTML = '⚠️ 离线模式：正在使用本地资源，部分功能可能受限';
    document.body.appendChild(notice);

    // 5秒后自动隐藏
    setTimeout(function() {
        if (notice.parentElement) {
            notice.remove();
        }
    }, 5000);
}

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查离线模式
    setTimeout(checkOfflineMode, 2000);
    // 为所有模态框添加样式
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.cssText += `
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1050;
            display: none;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
        `;

        // 为模态框内容添加样式
        const modalDialog = modal.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.style.cssText += `
                position: relative;
                width: auto;
                margin: 1.75rem auto;
                max-width: 500px;
                pointer-events: none;
            `;
        }

        const modalContent = modal.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.cssText += `
                position: relative;
                display: flex;
                flex-direction: column;
                width: 100%;
                pointer-events: auto;
                background-color: #fff;
                background-clip: padding-box;
                border: 1px solid rgba(0,0,0,.2);
                border-radius: 0.3rem;
                outline: 0;
                box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,.5);
            `;
        }

        const modalHeader = modal.querySelector('.modal-header');
        if (modalHeader) {
            modalHeader.style.cssText += `
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                padding: 1rem 1rem;
                border-bottom: 1px solid #dee2e6;
                border-top-left-radius: calc(0.3rem - 1px);
                border-top-right-radius: calc(0.3rem - 1px);
            `;
        }

        const modalBody = modal.querySelector('.modal-body');
        if (modalBody) {
            modalBody.style.cssText += `
                position: relative;
                flex: 1 1 auto;
                padding: 1rem;
            `;
        }

        const modalFooter = modal.querySelector('.modal-footer');
        if (modalFooter) {
            modalFooter.style.cssText += `
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding: 0.75rem;
                border-top: 1px solid #dee2e6;
                border-bottom-right-radius: calc(0.3rem - 1px);
                border-bottom-left-radius: calc(0.3rem - 1px);
            `;
        }
    });

    // 为按钮添加点击效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mousedown', function() {
            this.style.transform = 'scale(0.98)';
        });
        
        button.addEventListener('mouseup', function() {
            this.style.transform = 'scale(1)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // 为表单控件添加焦点效果
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = '#80bdff';
            this.style.boxShadow = '0 0 0 0.2rem rgba(0, 123, 255, 0.25)';
        });
        
        control.addEventListener('blur', function() {
            this.style.borderColor = '#ced4da';
            this.style.boxShadow = 'none';
        });
    });
});

// 工具函数
function showMessage(message, type = 'info') {
    // 创建消息提示
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 99999;
        max-width: 400px;
        margin: 0;
        box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    `;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove();" style="
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            padding: 0.75rem 0.75rem;
            background: none;
            border: 0;
            font-size: 1.25rem;
            line-height: 1;
            color: #000;
            opacity: 0.5;
            cursor: pointer;
        ">&times;</button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 3000);
}

// 导出到全局
window.showMessage = showMessage;
