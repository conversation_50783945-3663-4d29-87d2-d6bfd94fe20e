{% extends "base.html" %}

{% block title %}重复故障分析{% endblock %}

{% block styles %}
<style>
.analysis-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.param-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.result-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stats-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.table-responsive {
    max-height: 600px;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 10;
}

.repeat-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

.grid-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
}

.loading-spinner {
    display: none;
}

.analysis-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.btn-analyze {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-analyze:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-analyze:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}

.highlight-row {
    background-color: #fff3cd !important;
}

.critical-row {
    background-color: #f8d7da !important;
}

.warning-row {
    background-color: #d1ecf1 !important;
}

/* 日志样式 */
.log-entry {
    margin-bottom: 2px;
    padding: 2px 0;
    border-left: 3px solid transparent;
    padding-left: 8px;
}

.log-entry.log-info {
    border-left-color: #17a2b8;
    color: #17a2b8;
}

.log-entry.log-success {
    border-left-color: #28a745;
    color: #28a745;
}

.log-entry.log-warning {
    border-left-color: #ffc107;
    color: #ffc107;
}

.log-entry.log-error {
    border-left-color: #dc3545;
    color: #dc3545;
}

.log-entry.log-debug {
    border-left-color: #6c757d;
    color: #adb5bd;
}

.timestamp {
    color: #6c757d;
    font-weight: bold;
}

#logContainer {
    scrollbar-width: thin;
    scrollbar-color: #495057 #343a40;
}

#logContainer::-webkit-scrollbar {
    width: 8px;
}

#logContainer::-webkit-scrollbar-track {
    background: #343a40;
}

#logContainer::-webkit-scrollbar-thumb {
    background: #495057;
    border-radius: 4px;
}

#logContainer::-webkit-scrollbar-thumb:hover {
    background: #6c757d;
}

/* 详细分布样式 */
.detail-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.detail-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

.detail-btn.expanded {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.detail-expansion {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-left: 4px solid #667eea;
    padding: 16px;
    margin: 8px 0;
    border-radius: 8px;
    animation: slideDown 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding: 0 16px;
    }
    to {
        opacity: 1;
        max-height: 500px;
        padding: 16px;
    }
}

.month-section {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 3px solid #667eea;
}

.month-tag {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 13px;
    display: inline-block;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.2);
}

.date-item {
    display: inline-block;
    background: #f5f5f5;
    padding: 4px 8px;
    margin: 2px 4px 2px 0;
    border-radius: 12px;
    font-size: 12px;
    color: #555;
    border: 1px solid #e0e0e0;
}

.date-item.high-count {
    background: #ffebee;
    color: #c62828;
    border-color: #f8bbd9;
}

.date-item.medium-count {
    background: #fff3e0;
    color: #ef6c00;
    border-color: #ffcc02;
}

.summary-stats {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
    font-size: 13px;
    color: #666;
    border: 1px solid #e9ecef;
}

.expand-icon {
    transition: transform 0.3s ease;
    display: inline-block;
}

.expand-icon.rotated {
    transform: rotate(180deg);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .detail-expansion {
        padding: 12px;
        font-size: 14px;
    }

    .month-section {
        margin-bottom: 8px;
        padding: 8px;
    }

    .detail-btn {
        font-size: 11px;
        padding: 4px 8px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="bi bi-arrow-repeat text-warning"></i> 重复故障分析</h2>
                    <p class="text-muted mb-0">分析扇区数据中重复发生故障的小区，识别问题基站</p>
                </div>
                <div>
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="bi bi-house"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析参数设置 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="analysis-form">
                <h5 class="mb-3"><i class="bi bi-gear"></i> 分析参数设置</h5>
                <form id="analysisForm">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="targetDate" class="form-label">目标日期</label>
                            <input type="date" class="form-control" id="targetDate" name="target_date">
                            <small class="form-text text-muted">默认为数据库最新日期</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="analysisMode" class="form-label">分析模式</label>
                            <select class="form-select" id="analysisMode" name="analysis_mode">
                                <option value="auto" selected>🧠 智能分析（全历史数据）</option>
                                <option value="week">📅 本周分析（最近7天）</option>
                                <option value="month">📊 本月分析（当月1日至今）</option>
                                <option value="manual">⚙️ 自定义分析（手动指定）</option>
                            </select>
                            <small class="form-text text-muted">选择分析的时间范围</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="minOccurrences" class="form-label">最小重复次数</label>
                            <select class="form-select" id="minOccurrences" name="min_occurrences">
                                <option value="1">1次及以上（全量故障分析）</option>
                                <option value="2">2次及以上（重复故障分析）</option>
                                <option value="3">3次及以上</option>
                                <option value="4">4次及以上</option>
                                <option value="5" selected>5次及以上</option>
                                <option value="6">6次及以上</option>
                                <option value="7">7次及以上</option>
                                <option value="8">8次及以上</option>
                                <option value="9">9次及以上</option>
                                <option value="10">10次及以上</option>
                            </select>
                        </div>
                    </div>
                    <div class="row" id="manualDaysRow" style="display: none;">
                        <div class="col-md-4 mb-3">
                            <label for="daysBack" class="form-label">手动指定天数</label>
                            <select class="form-select" id="daysBack" name="days_back">
                                <option value="7">最近7天</option>
                                <option value="15">最近15天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="60">最近60天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                        <div class="col-md-8 mb-3 d-flex align-items-center">
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-info-circle"></i>
                                手动模式：您可以指定固定的分析天数，不考虑数据库中的实际数据范围
                            </div>
                        </div>
                    </div>
                    <!-- 智能分析说明 -->
                    <div class="row" id="autoAnalysisInfo" style="display: block;">
                        <div class="col-12 mb-3">
                            <div class="alert alert-success mb-0">
                                <i class="bi bi-cpu"></i>
                                <strong>智能分析：</strong>使用数据库中的全部历史数据进行分析，结果最全面准确
                            </div>
                        </div>
                    </div>

                    <!-- 本周分析说明 -->
                    <div class="row" id="weekAnalysisInfo" style="display: none;">
                        <div class="col-12 mb-3">
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-calendar-week"></i>
                                <strong>本周分析：</strong>分析最近7天的重复故障情况，适合短期趋势观察和周例会汇报
                            </div>
                        </div>
                    </div>

                    <!-- 本月分析说明 -->
                    <div class="row" id="monthAnalysisInfo" style="display: none;">
                        <div class="col-12 mb-3">
                            <div class="alert alert-warning mb-0">
                                <i class="bi bi-calendar-month"></i>
                                <strong>本月分析：</strong>分析当月1日至今的重复故障情况，适合月度考核和绩效评估
                            </div>
                        </div>
                    </div>

                    <!-- 自定义分析说明 -->
                    <div class="row" id="manualAnalysisInfo" style="display: none;">
                        <div class="col-12 mb-3">
                            <div class="alert alert-secondary mb-0">
                                <i class="bi bi-gear"></i>
                                <strong>自定义分析：</strong>手动指定分析天数，灵活控制分析范围
                            </div>
                        </div>
                    </div>

                    <!-- 时间范围预览 -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">📅 分析时间范围预览</h6>
                                    <div id="timeRangePreview" class="text-muted">
                                        <i class="bi bi-clock"></i> 将根据选择的模式自动计算时间范围
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 d-flex justify-content-center">
                            <button type="submit" class="btn btn-analyze" id="analyzeBtn" style="min-width: 200px;">
                                <i class="bi bi-cpu"></i> 开始智能分析
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div class="row mb-4 loading-spinner d-none" id="loadingSpinner">
        <div class="col-12 text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="mt-2">正在分析重复故障数据，请稍候...</p>
        </div>
    </div>

    <!-- 分析结果概览 -->
    <div class="row mb-4 d-none" id="summarySection">
        <div class="col-md-3 mb-3">
            <div class="card analysis-card param-card h-100">
                <div class="card-body text-center">
                    <h3 class="mb-1" id="analysisDate">-</h3>
                    <p class="mb-0">分析日期</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card analysis-card result-card h-100">
                <div class="card-body text-center">
                    <h3 class="mb-1" id="totalRepeated">-</h3>
                    <p class="mb-0">重复故障小区</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card analysis-card stats-card h-100">
                <div class="card-body text-center">
                    <h3 class="mb-1" id="totalCurrent">-</h3>
                    <p class="mb-0">当日故障小区</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card analysis-card h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                <div class="card-body text-center">
                    <h3 class="mb-1" id="repeatRate">-</h3>
                    <p class="mb-0">重复故障比例</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 全市综合分析模块 -->
    <div class="row mb-4 d-none" id="citywideSection">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-building"></i> 全市综合分析</h5>
                </div>
                <div class="card-body">
                    <!-- 核心指标概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <div class="card-body text-center">
                                    <h4 class="mb-1" id="citywideTotalCells">-</h4>
                                    <p class="mb-0">全市故障小区总数</p>
                                    <small class="opacity-75" id="citywideAnalysisPeriod">-</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                <div class="card-body text-center">
                                    <h4 class="mb-1" id="citywideRepeatedCells">-</h4>
                                    <p class="mb-0">重复故障小区数</p>
                                    <small class="opacity-75" id="citywideRepeatedRatio">-</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
                                <div class="card-body text-center">
                                    <h4 class="mb-1" id="citywideHighFreqCells">-</h4>
                                    <p class="mb-0">高频故障小区数</p>
                                    <small class="opacity-75">单日退服≥10次</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                <div class="card-body text-center">
                                    <h4 class="mb-1" id="citywideTargetCompliance">-</h4>
                                    <p class="mb-0">当日目标达标</p>
                                    <small class="opacity-75" id="citywideTargetDiff">-</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细统计表格 -->
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="bi bi-table"></i> 各网格统计详情</h6>
                            <div class="table-responsive" style="max-height: 400px;">
                                <table class="table table-hover table-sm" id="citywideGridTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>网格</th>
                                            <th>故障小区总数</th>
                                            <th>总退服次数</th>
                                            <th>重复故障小区</th>
                                            <th>重复故障比例</th>
                                            <th>当日故障小区</th>
                                            <th>当日退服次数</th>
                                        </tr>
                                    </thead>
                                    <tbody id="citywideGridTableBody">
                                        <!-- 动态填充 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 导出按钮 -->
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <button class="btn btn-success" onclick="exportCitywideAnalysis()">
                                <i class="bi bi-file-earmark-excel"></i> 导出全市综合分析报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 重复故障详细列表 -->
    <div class="row d-none" id="detailSection">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 重复故障小区详细列表</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                            <i class="bi bi-file-earmark-excel"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="detailTable">
                            <thead>
                                <tr>
                                    <th width="4%">序号</th>
                                    <th width="8%">网格</th>
                                    <th width="20%">小区名称</th>
                                    <th width="12%">基站编码</th>
                                    <th width="6%">重复次数</th>
                                    <th width="8%">首次故障</th>
                                    <th width="6%">跨度天数</th>
                                    <th width="6%">总退服</th>
                                    <th width="5%">平均</th>
                                    <th width="5%">当日</th>
                                    <th width="20%">详细分布</th>
                                </tr>
                            </thead>
                            <tbody id="detailTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计分析 -->
    <div class="row mt-4 d-none" id="statsSection">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-bar-chart"></i> 重复次数分布</h6>
                </div>
                <div class="card-body">
                    <div id="repeatCountsChart"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-geo-alt"></i> 网格故障统计</h6>
                    <small class="text-muted">重复故障小区数按≥5次标准统计，占比为≥5次小区退服数占该网格总退服数比重</small>
                </div>
                <div class="card-body">
                    <div id="gridStatsChart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时日志输出框 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="bi bi-terminal"></i> 实时调试日志</h6>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                            <i class="bi bi-trash"></i> 清空日志
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="toggleAutoScroll()" id="autoScrollBtn">
                            <i class="bi bi-arrow-down"></i> 自动滚动
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="logContainer" style="height: 300px; overflow-y: auto; background-color: #1e1e1e; color: #d4d4d4; font-family: 'Courier New', monospace; font-size: 12px; padding: 10px;">
                        <div class="log-entry text-info">
                            <span class="timestamp">[系统]</span> 重复故障分析系统已就绪，等待分析请求...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentAnalysisData = null;
let autoScroll = true;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取数据库中的最新日期作为默认值
    loadLatestDateFromDatabase();

    // 绑定表单提交事件
    document.getElementById('analysisForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performAnalysis();
    });

    // 绑定分析模式切换事件
    document.getElementById('analysisMode').addEventListener('change', function() {
        toggleAnalysisMode();
    });

    // 绑定目标日期变化事件
    document.getElementById('targetDate').addEventListener('change', updateTimeRangePreview);

    // 绑定天数变化事件
    document.getElementById('daysBack').addEventListener('input', updateTimeRangePreview);

    // 绑定最小重复次数变化事件
    document.getElementById('minOccurrences').addEventListener('change', function() {
        updateAnalysisButtonText();
        updateTimeRangePreview();
    });

    // 初始化显示
    toggleAnalysisMode();

    // 初始化日志
    addLog('系统初始化完成，页面加载成功', 'success');
});

// 日志管理函数
function addLog(message, type = 'info') {
    const logContainer = document.getElementById('logContainer');
    const timestamp = new Date().toLocaleTimeString();

    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;

    logContainer.appendChild(logEntry);

    // 自动滚动到底部
    if (autoScroll) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

function clearLogs() {
    const logContainer = document.getElementById('logContainer');
    logContainer.innerHTML = '<div class="log-entry text-info"><span class="timestamp">[系统]</span> 日志已清空</div>';
    addLog('日志清空完成', 'info');
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const btn = document.getElementById('autoScrollBtn');
    if (autoScroll) {
        btn.innerHTML = '<i class="bi bi-arrow-down"></i> 自动滚动';
        btn.className = 'btn btn-sm btn-outline-info';
        addLog('已启用自动滚动', 'info');
    } else {
        btn.innerHTML = '<i class="bi bi-pause"></i> 手动滚动';
        btn.className = 'btn btn-sm btn-outline-warning';
        addLog('已禁用自动滚动', 'warning');
    }
}

// 从数据库获取最新日期
function loadLatestDateFromDatabase() {
    addLog('正在获取数据库最新日期...', 'info');

    fetch('/api/get_latest_date', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success && result.latest_date) {
            document.getElementById('targetDate').value = result.latest_date;
            addLog(`已设置默认目标日期: ${result.latest_date}`, 'success');
        } else {
            // 如果获取失败，使用今天的日期作为备选
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('targetDate').value = today;
            addLog(`获取数据库日期失败，使用今天日期: ${today}`, 'warning');
        }
    })
    .catch(error => {
        // 如果请求失败，使用今天的日期作为备选
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('targetDate').value = today;
        addLog(`网络错误，使用今天日期: ${today}`, 'warning');
        console.error('获取最新日期失败:', error);
    });
}

// 切换分析模式
function toggleAnalysisMode() {
    const mode = document.getElementById('analysisMode').value;
    const manualRow = document.getElementById('manualDaysRow');
    const analyzeBtn = document.getElementById('analyzeBtn');

    // 隐藏所有说明
    hideAllAnalysisInfo();

    // 显示对应说明和控件
    switch(mode) {
        case 'auto':
            document.getElementById('autoAnalysisInfo').style.display = 'block';
            analyzeBtn.innerHTML = '<i class="bi bi-cpu"></i> 开始智能分析';
            addLog('切换到智能分析模式', 'info');
            break;

        case 'week':
            document.getElementById('weekAnalysisInfo').style.display = 'block';
            analyzeBtn.innerHTML = '<i class="bi bi-calendar-week"></i> 开始本周分析';
            addLog('切换到本周分析模式', 'info');
            break;

        case 'month':
            document.getElementById('monthAnalysisInfo').style.display = 'block';
            analyzeBtn.innerHTML = '<i class="bi bi-calendar-month"></i> 开始本月分析';
            addLog('切换到本月分析模式', 'info');
            break;

        case 'manual':
            document.getElementById('manualAnalysisInfo').style.display = 'block';
            manualRow.style.display = 'block';
            analyzeBtn.innerHTML = '<i class="bi bi-gear"></i> 开始自定义分析';
            addLog('切换到自定义分析模式', 'info');
            break;
    }

    // 更新时间范围预览
    updateTimeRangePreview();

    // 更新按钮文本（根据最小重复次数动态调整）
    updateAnalysisButtonText();
}

// 隐藏所有分析说明
function hideAllAnalysisInfo() {
    document.getElementById('autoAnalysisInfo').style.display = 'none';
    document.getElementById('weekAnalysisInfo').style.display = 'none';
    document.getElementById('monthAnalysisInfo').style.display = 'none';
    document.getElementById('manualAnalysisInfo').style.display = 'none';
    document.getElementById('manualDaysRow').style.display = 'none';
}

// 根据最小重复次数动态更新按钮文本
function updateAnalysisButtonText() {
    const analyzeBtn = document.getElementById('analyzeBtn');
    const analysisMode = document.getElementById('analysisMode').value;
    const minOccurrences = parseInt(document.getElementById('minOccurrences').value);

    // 根据最小重复次数确定分析类型
    const isFullAnalysis = minOccurrences === 1;
    const analysisType = isFullAnalysis ? '故障小区分析' : '重复故障分析';

    // 根据分析模式和类型更新按钮文本
    switch(analysisMode) {
        case 'auto':
            analyzeBtn.innerHTML = `<i class="bi bi-cpu"></i> 开始智能${analysisType}`;
            break;
        case 'week':
            analyzeBtn.innerHTML = `<i class="bi bi-calendar-week"></i> 开始本周${analysisType}`;
            break;
        case 'month':
            analyzeBtn.innerHTML = `<i class="bi bi-calendar-month"></i> 开始本月${analysisType}`;
            break;
        case 'manual':
            analyzeBtn.innerHTML = `<i class="bi bi-gear"></i> 开始自定义${analysisType}`;
            break;
    }

    // 更新页面标题中的分析类型提示
    updateAnalysisTypeHint(isFullAnalysis);
}

// 更新分析类型提示
function updateAnalysisTypeHint(isFullAnalysis) {
    // 可以在这里添加页面上的其他提示更新
    const analysisTypeText = isFullAnalysis ? '全量故障分析' : '重复故障分析';
    addLog(`分析类型: ${analysisTypeText}`, 'debug');
}

// 更新时间范围预览
function updateTimeRangePreview() {
    const mode = document.getElementById('analysisMode').value;
    const targetDate = document.getElementById('targetDate').value || getTodayDate();
    const preview = document.getElementById('timeRangePreview');

    const target = new Date(targetDate);
    let description;

    switch(mode) {
        case 'auto':
            description = `从数据库最早日期到 ${targetDate}`;
            break;

        case 'week':
            const weekStart = new Date(target.getTime() - 6 * 24 * 60 * 60 * 1000);
            description = `${formatDate(weekStart)} 到 ${targetDate} (共7天)`;
            break;

        case 'month':
            const monthStart = new Date(target.getFullYear(), target.getMonth(), 1);
            const days = Math.ceil((target - monthStart) / (24 * 60 * 60 * 1000)) + 1;
            description = `${formatDate(monthStart)} 到 ${targetDate} (共${days}天)`;
            break;

        case 'manual':
            const daysBack = document.getElementById('daysBack').value || 30;
            const manualStart = new Date(target.getTime() - (daysBack - 1) * 24 * 60 * 60 * 1000);
            description = `${formatDate(manualStart)} 到 ${targetDate} (共${daysBack}天)`;
            break;
    }

    preview.innerHTML = `<i class="bi bi-clock"></i> ${description}`;
}

// 格式化日期
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

// 获取今天日期
function getTodayDate() {
    return new Date().toISOString().split('T')[0];
}

// 执行分析
function performAnalysis() {
    const formData = new FormData(document.getElementById('analysisForm'));
    const analysisMode = formData.get('analysis_mode');

    const data = {
        target_date: formData.get('target_date') || null,
        analysis_mode: analysisMode,
        min_occurrences: parseInt(formData.get('min_occurrences'))
    };

    // 只有手动模式才传递days_back参数
    if (analysisMode === 'manual') {
        data.days_back = parseInt(formData.get('days_back'));
    }

    addLog('开始重复故障分析...', 'info');

    if (analysisMode === 'auto') {
        addLog(`智能分析参数: 目标日期=${data.target_date || '最新'}, 模式=智能分析, 最小重复=${data.min_occurrences}次`, 'debug');
        addLog('智能模式将自动计算最优分析时间范围...', 'info');
    } else {
        addLog(`手动分析参数: 目标日期=${data.target_date || '最新'}, 分析天数=${data.days_back}, 最小重复=${data.min_occurrences}次`, 'debug');
    }

    // 显示加载状态
    showLoading(true);
    hideResults();

    addLog('正在发送分析请求到后台...', 'info');

    // 发送分析请求
    fetch('/api/repeated_outages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        addLog(`收到后台响应，状态码: ${response.status}`, response.ok ? 'success' : 'error');
        return response.json();
    })
    .then(result => {
        showLoading(false);

        // 显示后台调试日志
        if (result.debug_logs && result.debug_logs.length > 0) {
            addLog('=== 后台分析日志 ===', 'info');
            result.debug_logs.forEach(log => {
                addLog(`[后台] ${log.message}`, log.level);
            });
            addLog('=== 后台日志结束 ===', 'info');
        }

        if (result.success) {
            addLog('分析完成，开始处理结果数据...', 'success');
            currentAnalysisData = result.data;
            displayResults(result.data);
            addLog(`分析结果: 发现 ${result.data.summary.total_repeated} 个重复故障小区`, 'success');

            // 同时执行全市综合分析
            performCitywideAnalysis(data);
        } else {
            addLog(`分析失败: ${result.error}`, 'error');
            showAlert('分析失败: ' + result.error, 'danger');

            // 即使失败也显示调试日志
            if (result.debug_logs && result.debug_logs.length > 0) {
                addLog('=== 错误调试日志 ===', 'error');
                result.debug_logs.forEach(log => {
                    addLog(`[后台] ${log.message}`, log.level);
                });
            }
        }
    })
    .catch(error => {
        showLoading(false);
        addLog(`网络请求异常: ${error.message}`, 'error');
        console.error('分析请求失败:', error);
        showAlert('网络错误，请重试', 'danger');
    });
}

// 显示/隐藏加载状态
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    const btn = document.getElementById('analyzeBtn');
    
    if (show) {
        spinner.classList.remove('d-none');
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
    } else {
        spinner.classList.add('d-none');
        btn.disabled = false;
        btn.innerHTML = '<i class="bi bi-search"></i> 开始分析';
    }
}

// 隐藏结果
function hideResults() {
    document.getElementById('summarySection').classList.add('d-none');
    document.getElementById('detailSection').classList.add('d-none');
    document.getElementById('statsSection').classList.add('d-none');
}

// 显示分析结果
function displayResults(data) {
    addLog('开始渲染分析结果...', 'info');

    // 显示概览信息
    const analysisInfo = data.analysis_info;
    document.getElementById('analysisDate').textContent = analysisInfo.target_date;
    document.getElementById('totalRepeated').textContent = data.summary.total_repeated;
    document.getElementById('totalCurrent').textContent = data.summary.total_current_day;
    document.getElementById('repeatRate').textContent = data.summary.repeat_rate + '%';

    // 显示分析模式信息
    if (analysisInfo.analysis_mode === 'auto') {
        addLog(`智能分析完成: ${analysisInfo.start_date} 到 ${analysisInfo.target_date} (共${analysisInfo.actual_days}天)`, 'success');
    } else {
        addLog(`手动分析完成: ${analysisInfo.start_date} 到 ${analysisInfo.target_date} (共${analysisInfo.actual_days}天)`, 'success');
    }

    addLog(`概览数据渲染完成: 分析日期=${analysisInfo.target_date}`, 'debug');

    // 显示详细列表
    addLog(`开始渲染详细表格，共 ${data.repeated_outages.length} 条记录...`, 'info');
    displayDetailTable(data.repeated_outages);

    // 显示统计图表
    addLog('开始渲染统计图表...', 'info');
    displayCharts(data);

    // 显示所有结果区域
    document.getElementById('summarySection').classList.remove('d-none');
    document.getElementById('detailSection').classList.remove('d-none');
    document.getElementById('statsSection').classList.remove('d-none');

    addLog('所有结果渲染完成！', 'success');
}

// 显示详细表格
function displayDetailTable(outages) {
    const tbody = document.getElementById('detailTableBody');
    tbody.innerHTML = '';

    addLog(`清空表格，准备渲染 ${outages.length} 行数据`, 'debug');

    let criticalCount = 0, warningCount = 0, highlightCount = 0;

    outages.forEach((outage, index) => {
        const row = document.createElement('tr');

        // 根据重复次数设置行样式
        if (outage.repeat_count >= 10) {
            row.classList.add('critical-row');
            criticalCount++;
        } else if (outage.repeat_count >= 5) {
            row.classList.add('warning-row');
            warningCount++;
        } else if (outage.repeat_count >= 3) {
            row.classList.add('highlight-row');
            highlightCount++;
        }

        row.innerHTML = `
            <td>${index + 1}</td>
            <td><span class="badge bg-primary grid-badge">${outage.grid}</span></td>
            <td class="text-truncate" style="max-width: 200px;" title="${outage.cell_name}">${outage.cell_name}</td>
            <td><code class="small">${outage.base_station_code}</code></td>
            <td><span class="badge bg-danger repeat-badge">${outage.repeat_count}次</span></td>
            <td><small>${outage.first_date}</small></td>
            <td><span class="badge bg-info">${outage.span_days}天</span></td>
            <td><strong>${outage.total_outages}</strong></td>
            <td>${outage.avg_outages}</td>
            <td><span class="badge bg-warning">${outage.current_outage}</span></td>
            <td>
                <button class="detail-btn" onclick="toggleDetailExpansion(${index})">
                    📊 ${outage.repeat_count}次分布
                    <span class="expand-icon">▼</span>
                </button>
            </td>
        `;

        tbody.appendChild(row);
    });

    addLog(`表格渲染完成: 严重${criticalCount}个, 警告${warningCount}个, 关注${highlightCount}个`, 'debug');
}

// 切换详细分布展开/收起
function toggleDetailExpansion(index) {
    const row = document.querySelector(`#detailTable tbody tr:nth-child(${index + 1})`);
    const button = row.querySelector('.detail-btn');
    const icon = button.querySelector('.expand-icon');

    // 检查是否已经展开
    let expansionRow = row.nextElementSibling;
    if (expansionRow && expansionRow.classList.contains('expansion-row')) {
        // 已展开，收起
        expansionRow.remove();
        button.classList.remove('expanded');
        icon.classList.remove('rotated');
        icon.textContent = '▼';
        addLog(`收起小区详细分布: ${currentAnalysisData.repeated_outages[index].cell_name}`, 'debug');
    } else {
        // 未展开，展开
        const outage = currentAnalysisData.repeated_outages[index];
        const expansionContent = generateDetailExpansionContent(outage);

        expansionRow = document.createElement('tr');
        expansionRow.classList.add('expansion-row');
        expansionRow.innerHTML = `
            <td colspan="11">
                <div class="detail-expansion">
                    ${expansionContent}
                </div>
            </td>
        `;

        row.insertAdjacentElement('afterend', expansionRow);
        button.classList.add('expanded');
        icon.classList.add('rotated');
        icon.textContent = '▲';
        addLog(`展开小区详细分布: ${outage.cell_name}`, 'debug');
    }
}

// 生成详细分布内容
function generateDetailExpansionContent(outage) {
    let content = `
        <div class="mb-3">
            <h6 class="mb-3">📅 故障时间分布详情 - ${outage.cell_name}</h6>
        </div>
    `;

    // 按月份显示分布
    const monthlyDist = outage.monthly_distribution;
    for (const [month, data] of Object.entries(monthlyDist).sort()) {
        content += `
            <div class="month-section">
                <div class="month-tag">🗓️ ${month} (${data.days}天)</div>
                <div class="mt-2">
        `;

        // 显示每天的详细信息
        data.details.forEach(detail => {
            const countClass = detail.count >= 5 ? 'high-count' :
                              detail.count >= 3 ? 'medium-count' : '';
            content += `
                <span class="date-item ${countClass}">
                    ${detail.date}(${detail.count}次)
                </span>
            `;
        });

        content += `
                </div>
            </div>
        `;
    }

    // 添加统计汇总
    const totalDays = outage.repeat_count;
    const avgPerDay = (outage.total_outages / totalDays).toFixed(1);
    const monthCount = Object.keys(monthlyDist).length;

    content += `
        <div class="summary-stats">
            📊 <strong>统计汇总:</strong>
            总计${totalDays}天故障，累计${outage.total_outages}次退服，平均${avgPerDay}次/天，
            跨越${monthCount}个月${monthCount > 1 ? '，属于长期重复故障 ⚠️' : ''}
        </div>
    `;

    return content;
}

// 显示统计图表
function displayCharts(data) {
    // 重复次数分布图表
    const repeatCounts = Object.entries(data.repeat_counts)
        .sort((a, b) => parseInt(b[0]) - parseInt(a[0]))
        .map(([count, num]) => `${count}次: ${num}个小区`)
        .join('<br>');
    
    document.getElementById('repeatCountsChart').innerHTML = `
        <div class="text-center">
            <div class="mb-3">${repeatCounts}</div>
        </div>
    `;
    
    // 网格统计图表（固定按≥5次统计）
    const gridStats = Object.entries(data.grid_stats)
        .sort((a, b) => (b[1].count_5plus || b[1].count) - (a[1].count_5plus || a[1].count))
        .map(([grid, stats]) => {
            // 优先使用count_5plus，如果不存在则使用count（向后兼容）
            const count = stats.count_5plus !== undefined ? stats.count_5plus : stats.count;
            const totalOutages = stats.total_outages;
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-primary">${grid}</span>
                    <span>${count}个小区(≥5次) (${totalOutages}次)</span>
                </div>
            `;
        })
        .join('');
    
    document.getElementById('gridStatsChart').innerHTML = gridStats;
}

// 导出Excel
function exportToExcel() {
    if (!currentAnalysisData) {
        showAlert('没有可导出的数据', 'warning');
        return;
    }
    
    showAlert('Excel导出功能开发中...', 'info');
}

// 显示提示消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'x-circle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 导出Excel功能
function exportToExcel() {
    if (!currentAnalysisData) {
        showAlert('请先进行重复故障分析', 'warning');
        return;
    }

    addLog('开始导出Excel文件...', 'info');

    // 准备导出数据
    const exportData = {
        target_date: currentAnalysisData.analysis_info.target_date,
        analysis_mode: currentAnalysisData.analysis_info.analysis_mode,
        min_occurrences: currentAnalysisData.analysis_info.min_occurrences
    };

    // 如果是手动模式，添加days_back参数
    if (currentAnalysisData.analysis_info.analysis_mode === 'manual') {
        exportData.days_back = currentAnalysisData.analysis_info.actual_days;
    }

    // 显示导出按钮加载状态
    const exportBtn = document.querySelector('button[onclick="exportToExcel()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
    exportBtn.disabled = true;

    // 发送导出请求
    fetch('/api/export_repeated_outages_excel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportData)
    })
    .then(response => {
        if (response.ok) {
            addLog('Excel文件生成成功，开始下载...', 'success');
            return response.blob();
        } else {
            throw new Error(`导出失败: HTTP ${response.status}`);
        }
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `重复故障分析_${currentAnalysisData.analysis_info.target_date}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        addLog(`Excel文件下载完成: ${a.download}`, 'success');
        showAlert('Excel文件导出成功！', 'success');
    })
    .catch(error => {
        addLog(`Excel导出失败: ${error.message}`, 'error');
        showAlert('Excel导出失败，请重试', 'danger');
        console.error('Excel导出错误:', error);
    })
    .finally(() => {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    });
}

// 全市综合分析相关函数
function performCitywideAnalysis(analysisParams) {
    addLog('开始全市综合分析...', 'info');

    // 发送全市综合分析请求
    fetch('/api/citywide_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(analysisParams)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            addLog('全市综合分析完成', 'success');
            displayCitywideResults(result);
        } else {
            addLog(`全市综合分析失败: ${result.error}`, 'error');
        }
    })
    .catch(error => {
        addLog(`全市综合分析请求失败: ${error.message}`, 'error');
        console.error('全市综合分析错误:', error);
    });
}

function displayCitywideResults(result) {
    // 显示全市综合分析模块
    document.getElementById('citywideSection').classList.remove('d-none');

    const stats = result.citywide_stats;
    const target = result.target_analysis;
    const highFreq = result.high_frequency_analysis;
    const gridStats = result.grid_stats;

    // 更新核心指标
    document.getElementById('citywideTotalCells').textContent = stats.total_fault_cells;
    document.getElementById('citywideAnalysisPeriod').textContent = `分析期间: ${result.analysis_info.start_date} 至 ${result.analysis_info.target_date}`;

    document.getElementById('citywideRepeatedCells').textContent = stats.repeated_fault_cells;
    document.getElementById('citywideRepeatedRatio').textContent = `占比: ${stats.repeated_fault_ratio}%`;

    document.getElementById('citywideHighFreqCells').textContent = highFreq.high_freq_cells;

    // 目标达标情况
    const complianceText = target.daily_compliance ? '✓ 达标' : '✗ 超标';
    const complianceColor = target.daily_compliance ? 'text-success' : 'text-danger';
    document.getElementById('citywideTargetCompliance').innerHTML = `<span class="${complianceColor}">${complianceText}</span>`;

    const diffText = target.daily_difference >= 0 ? `余量: ${target.daily_difference}` : `超出: ${Math.abs(target.daily_difference)}`;
    document.getElementById('citywideTargetDiff').textContent = diffText;

    // 更新网格统计表格
    updateCitywideGridTable(gridStats);

    addLog(`全市综合分析结果: 总故障小区${stats.total_fault_cells}个, 重复故障${stats.repeated_fault_cells}个, 高频故障${highFreq.high_freq_cells}个`, 'success');
}

function updateCitywideGridTable(gridStats) {
    const tbody = document.getElementById('citywideGridTableBody');
    tbody.innerHTML = '';

    gridStats.forEach(grid => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-primary">${grid.grid}</span></td>
            <td>${grid.total_fault_cells}</td>
            <td>${grid.total_outages}</td>
            <td>${grid.repeated_fault_cells}</td>
            <td>${grid.repeated_ratio}%</td>
            <td>${grid.current_fault_cells}</td>
            <td>${grid.current_outages}</td>
        `;
        tbody.appendChild(row);
    });
}

function exportCitywideAnalysis() {
    if (!currentAnalysisData) {
        showAlert('请先执行分析', 'warning');
        return;
    }

    addLog('开始导出全市综合分析报告...', 'info');

    const exportData = {
        target_date: currentAnalysisData.analysis_info.target_date,
        analysis_mode: currentAnalysisData.analysis_info.analysis_mode,
        min_occurrences: currentAnalysisData.analysis_info.min_occurrences
    };

    // 如果是手动模式，添加days_back参数
    if (currentAnalysisData.analysis_info.analysis_mode === 'manual') {
        exportData.days_back = currentAnalysisData.analysis_info.actual_days;
    }

    // 显示导出按钮加载状态
    const exportBtn = document.querySelector('button[onclick="exportCitywideAnalysis()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
    exportBtn.disabled = true;

    // 发送导出请求
    fetch('/api/export_citywide_analysis_excel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportData)
    })
    .then(response => {
        if (response.ok) {
            addLog('全市综合分析Excel文件生成成功，开始下载...', 'success');
            return response.blob();
        } else {
            throw new Error(`导出失败: HTTP ${response.status}`);
        }
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `全市综合分析报告_${currentAnalysisData.analysis_info.target_date}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        addLog(`全市综合分析Excel文件下载完成: ${a.download}`, 'success');
        showAlert('全市综合分析报告导出成功！', 'success');
    })
    .catch(error => {
        addLog(`全市综合分析Excel导出失败: ${error.message}`, 'error');
        showAlert('全市综合分析Excel导出失败，请重试', 'danger');
        console.error('全市综合分析Excel导出错误:', error);
    })
    .finally(() => {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    });
}
</script>
{% endblock %}
