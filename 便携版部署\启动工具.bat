@echo off
chcp 65001 > nul
title 菏泽数据提取工具
cd /d "%~dp0"

echo ========================================
echo      菏泽数据提取工具 v2.0
echo ========================================
echo.

REM 检查Python环境
if exist "python\python.exe" (
    echo ✅ 发现便携版Python环境
    set PYTHON_PATH=%cd%\python\python.exe
) else (
    echo ❌ 未找到便携版Python，检查系统Python...
    python --version > nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误：未找到Python环境
        echo.
        echo 解决方案：
        echo 1. 下载便携版Python到python文件夹
        echo 2. 或者安装Python到系统
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ 使用系统Python环境
        set PYTHON_PATH=python
    )
)

echo.
echo 正在启动Web服务器...
echo 请稍候，首次启动可能需要一些时间...
echo.

REM 检查依赖是否安装
echo 检查依赖包...
%PYTHON_PATH% -c "import flask" > nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    %PYTHON_PATH% -m pip install -r app\requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
)

echo.
echo 🚀 启动成功！
echo.
echo 📂 Web界面地址：http://127.0.0.1:5000
echo 📂 数据上传地址：http://127.0.0.1:5000/upload
echo.
echo 💡 提示：
echo - 程序运行时请保持此窗口打开
echo - 关闭此窗口将停止服务
echo - 如需停止服务，按 Ctrl+C
echo.

REM 启动应用
cd app
%PYTHON_PATH% web_app.py

echo.
echo 服务已停止
pause 