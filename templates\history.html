{% extends "base.html" %}

{% block title %}历史数据查询 - 菏泽数据提取工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary">
                <i class="bi bi-clock-history"></i>
                历史数据查询
            </h2>
            <div>
                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回上传
                </a>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
            </div>
        </div>

        <!-- 数据统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-primary">
                    <div class="card-body text-center">
                        <i class="bi bi-database display-4 text-primary"></i>
                        <h5 class="card-title mt-3">总导入次数</h5>
                        <h3 class="text-primary" id="totalImports">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-day display-4 text-info"></i>
                        <h5 class="card-title mt-3">日度数据</h5>
                        <h3 class="text-info" id="dailyCount">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-month display-4 text-warning"></i>
                        <h5 class="card-title mt-3">月度数据</h5>
                        <h3 class="text-warning" id="monthlyCount">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <i class="bi bi-check-circle display-4 text-success"></i>
                        <h5 class="card-title mt-3">校正次数</h5>
                        <h3 class="text-success" id="correctionCount">-</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导入记录表格 -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    导入记录
                    <span class="badge bg-light text-dark ms-2" id="recordCount">0</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0" id="importTable">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>日期</th>
                                <th>导入类型</th>
                                <th>日度总计</th>
                                <th>月度总计</th>
                                <th>数据校正</th>
                                <th>导入时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="importTableBody">
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="bi bi-hourglass-split"></i>
                                    正在加载数据...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 数据详情模态框 -->
        <div class="modal fade" id="dataModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">数据详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modalContent">
                            <div class="text-center py-4">
                                <i class="bi bi-hourglass-split"></i>
                                正在加载数据...
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="downloadModalData()">
                            <i class="bi bi-download"></i> 下载数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentModalData = null;

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    loadImportRecords();
});

// 加载导入记录
function loadImportRecords() {
    fetch('/api/import_records')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayImportRecords(data.data);
                updateStatistics(data.data);
            } else {
                showError('加载数据失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
}

// 显示导入记录
function displayImportRecords(records) {
    const tbody = document.getElementById('importTableBody');
    const recordCount = document.getElementById('recordCount');
    
    recordCount.textContent = records.length;
    
    if (records.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="bi bi-inbox"></i>
                    暂无导入记录
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = records.map(record => {
        const importTypeText = {
            'daily': '日度数据',
            'monthly': '月度数据',
            'both': '日度+月度'
        }[record.import_type] || record.import_type;
        
        const correctionBadge = record.correction_applied 
            ? `<span class="badge bg-warning">已校正 (${record.correction_amount > 0 ? '+' : ''}${record.correction_amount})</span>`
            : `<span class="badge bg-success">无需校正</span>`;
        
        const dailyTotal = record.daily_total !== null ? record.daily_total : '-';
        const monthlyTotal = record.monthly_total !== null ? record.monthly_total : '-';
        
        return `
            <tr>
                <td><strong>${record.date}</strong></td>
                <td>
                    <span class="badge ${record.import_type === 'both' ? 'bg-primary' : record.import_type === 'daily' ? 'bg-info' : 'bg-warning'}">
                        ${importTypeText}
                    </span>
                </td>
                <td>${dailyTotal}</td>
                <td>${monthlyTotal}</td>
                <td>${correctionBadge}</td>
                <td><small class="text-muted">${new Date(record.created_at).toLocaleString()}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewData('${record.date}', '${record.import_type}')">
                        <i class="bi bi-eye"></i> 查看
                    </button>
                    ${record.import_type === 'both' ?
                        `<a href="/results?date=${record.date}&from_history=true" class="btn btn-sm btn-success">
                            <i class="bi bi-graph-up"></i> 处理结果
                        </a>` : ''
                    }
                </td>
            </tr>
        `;
    }).join('');
}

// 更新统计信息
function updateStatistics(records) {
    const totalImports = records.length;
    const dailyCount = records.filter(r => r.import_type === 'daily' || r.import_type === 'both').length;
    const monthlyCount = records.filter(r => r.import_type === 'monthly' || r.import_type === 'both').length;
    const correctionCount = records.filter(r => r.correction_applied).length;
    
    document.getElementById('totalImports').textContent = totalImports;
    document.getElementById('dailyCount').textContent = dailyCount;
    document.getElementById('monthlyCount').textContent = monthlyCount;
    document.getElementById('correctionCount').textContent = correctionCount;
}

// 查看数据详情
function viewData(date, importType) {
    const modal = new bootstrap.Modal(document.getElementById('dataModal'));
    const modalTitle = document.getElementById('modalTitle');
    const modalContent = document.getElementById('modalContent');
    
    modalTitle.textContent = `${date} 数据详情`;
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <i class="bi bi-hourglass-split"></i>
            正在加载数据...
        </div>
    `;
    
    modal.show();
    
    // 加载数据（这里可以扩展为实际的API调用）
    modalContent.innerHTML = `
        <div class="alert alert-info">
            <h6>数据查询功能</h6>
            <p>日期: ${date}</p>
            <p>类型: ${importType}</p>
            <p class="mb-0">详细的数据查询功能正在开发中，将支持查看历史数据的完整内容。</p>
        </div>
    `;
}

// 下载模态框数据
function downloadModalData() {
    alert('下载功能正在开发中');
}

// 刷新数据
function refreshData() {
    loadImportRecords();
}

// 显示错误信息
function showError(message) {
    const tbody = document.getElementById('importTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="7" class="text-center text-danger py-4">
                <i class="bi bi-exclamation-triangle"></i>
                ${message}
            </td>
        </tr>
    `;
}
</script>
{% endblock %}
