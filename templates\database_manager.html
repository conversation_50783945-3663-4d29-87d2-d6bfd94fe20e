<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - 菏泽数据处理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        
        .table-container {
            max-height: 500px;
            overflow-y: auto;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .filter-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #28a745;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container-fluid py-4">
        <!-- 导航栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="bi bi-database"></i>
                                数据库管理
                            </h4>
                            <div>
                                <a href="/" class="btn btn-outline-light me-2">
                                    <i class="bi bi-house"></i> 返回首页
                                </a>
                                <button class="btn btn-outline-light" onclick="refreshStats()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新统计
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库统计信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <h5 class="mb-3">
                        <i class="bi bi-bar-chart"></i>
                        数据库统计信息
                    </h5>
                    <div class="row" id="statsContainer">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">日度数据</h6>
                                <h3 class="text-primary" id="dailyTotal">-</h3>
                                <small class="text-muted">总记录数</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">月度数据</h6>
                                <h3 class="text-success" id="monthlyTotal">-</h3>
                                <small class="text-muted">总记录数</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">导入记录</h6>
                                <h3 class="text-warning" id="importTotal">-</h3>
                                <small class="text-muted">导入次数</small>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <small class="text-muted">日度数据时间范围：</small>
                            <span id="dailyDateRange">-</span>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">月度数据时间范围：</small>
                            <span id="monthlyDateRange">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询和导出控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-section">
                    <h5 class="mb-3">
                        <i class="bi bi-funnel"></i>
                        数据查询与导出
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">数据表</label>
                            <select class="form-select" id="tableSelect">
                                <option value="daily_data">日度数据</option>
                                <option value="monthly_data">月度数据</option>
                                <option value="import_records">导入记录</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">区县</label>
                            <select class="form-select" id="districtSelect">
                                <option value="all">全部区县</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" onclick="queryData()">
                                <i class="bi bi-search"></i> 查询数据
                            </button>
                            <button class="btn btn-success me-2" onclick="exportData('excel')">
                                <i class="bi bi-file-earmark-excel"></i> 导出Excel
                            </button>
                            <button class="btn btn-warning" onclick="exportData('csv')">
                                <i class="bi bi-file-earmark-text"></i> 导出CSV
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据显示区域 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-table"></i>
                            数据查询结果
                            <span class="badge bg-light text-dark ms-2" id="recordCount">0 条记录</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="loading" id="loadingIndicator">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在查询数据...</p>
                        </div>
                        
                        <div class="table-container" id="dataTableContainer">
                            <div class="text-center p-4 text-muted">
                                <i class="bi bi-database" style="font-size: 3rem;"></i>
                                <p class="mt-2">请选择查询条件并点击"查询数据"按钮</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            loadDistricts();
        });

        // 刷新统计信息
        function refreshStats() {
            fetch('/api/database/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('dailyTotal').textContent = stats.daily_total || 0;
                        document.getElementById('monthlyTotal').textContent = stats.monthly_total || 0;
                        document.getElementById('importTotal').textContent = stats.import_total || 0;
                        
                        const dailyRange = stats.daily_date_range;
                        const monthlyRange = stats.monthly_date_range;
                        
                        document.getElementById('dailyDateRange').textContent = 
                            dailyRange.start && dailyRange.end ? `${dailyRange.start} 至 ${dailyRange.end}` : '暂无数据';
                        document.getElementById('monthlyDateRange').textContent = 
                            monthlyRange.start && monthlyRange.end ? `${monthlyRange.start} 至 ${monthlyRange.end}` : '暂无数据';
                    }
                })
                .catch(error => {
                    console.error('获取统计信息失败:', error);
                });
        }

        // 加载区县列表
        function loadDistricts() {
            fetch('/api/database/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.stats.districts) {
                        const districtSelect = document.getElementById('districtSelect');
                        const districts = data.stats.districts.all;
                        
                        // 清空现有选项（保留"全部区县"）
                        districtSelect.innerHTML = '<option value="all">全部区县</option>';
                        
                        // 添加区县选项
                        districts.forEach(district => {
                            const option = document.createElement('option');
                            option.value = district;
                            option.textContent = district;
                            districtSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载区县列表失败:', error);
                });
        }

        // 查询数据
        function queryData() {
            const table = document.getElementById('tableSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const district = document.getElementById('districtSelect').value;
            
            // 显示加载指示器
            document.getElementById('loadingIndicator').style.display = 'block';
            document.getElementById('dataTableContainer').innerHTML = '';
            
            const queryData = {
                table: table,
                start_date: startDate || null,
                end_date: endDate || null,
                district: district === 'all' ? null : district
            };
            
            fetch('/api/database/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(queryData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingIndicator').style.display = 'none';
                
                if (data.success) {
                    displayData(data.data, data.columns);
                    document.getElementById('recordCount').textContent = `${data.total} 条记录`;
                } else {
                    showError('查询失败: ' + data.error);
                }
            })
            .catch(error => {
                document.getElementById('loadingIndicator').style.display = 'none';
                showError('查询失败: ' + error.message);
            });
        }

        // 显示数据
        function displayData(data, columns) {
            if (!data || data.length === 0) {
                document.getElementById('dataTableContainer').innerHTML = `
                    <div class="text-center p-4 text-muted">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">没有找到符合条件的数据</p>
                    </div>
                `;
                return;
            }
            
            let tableHtml = `
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-success sticky-top">
                        <tr>
            `;
            
            columns.forEach(col => {
                tableHtml += `<th class="text-center">${col}</th>`;
            });
            
            tableHtml += `
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(row => {
                tableHtml += '<tr>';
                columns.forEach(col => {
                    let value = row[col];
                    if (value === null || value === undefined) {
                        value = '-';
                    }
                    tableHtml += `<td class="text-center">${value}</td>`;
                });
                tableHtml += '</tr>';
            });
            
            tableHtml += `
                    </tbody>
                </table>
            `;
            
            document.getElementById('dataTableContainer').innerHTML = tableHtml;
        }

        // 导出数据
        function exportData(format) {
            const table = document.getElementById('tableSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const district = document.getElementById('districtSelect').value;
            
            const exportData = {
                table: table,
                start_date: startDate || null,
                end_date: endDate || null,
                district: district === 'all' ? null : district,
                format: format
            };
            
            fetch('/api/database/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = `/uploads/${data.filename}`;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    showSuccess(`导出成功！已导出 ${data.records} 条记录`);
                } else {
                    showError('导出失败: ' + data.error);
                }
            })
            .catch(error => {
                showError('导出失败: ' + error.message);
            });
        }

        // 显示成功消息
        function showSuccess(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // 显示错误消息
        function showError(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
    </script>
</body>
</html>
