<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-2">
                <div class="d-flex flex-column flex-shrink-0 p-3 bg-light" style="height: 100vh;">
                    <h5>菏泽数据提取工具</h5>
                    <hr>
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item"><a href="/" class="nav-link">首页</a></li>
                        <li class="nav-item"><a href="/sector_data_manager" class="nav-link">扇区数据管理</a></li>
                        <li class="nav-item"><a href="/trends_analysis" class="nav-link">趋势分析</a></li>

                        <li class="nav-item"><a href="/data_manager" class="nav-link active">数据库管理</a></li>
                        <li class="nav-item"><a href="/history" class="nav-link">历史记录</a></li>
                        <li class="nav-item"><a href="/email_config" class="nav-link">邮件配置</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-10">
                <div class="container mt-4">
                    <h2>数据库管理</h2>
                    
                    <!-- 统计信息 -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">日度数据</h5>
                                    <h3 class="text-primary" id="dailyCount">-</h3>
                                    <small class="text-muted">总记录数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">月度数据</h5>
                                    <h3 class="text-warning" id="monthlyCount">-</h3>
                                    <small class="text-muted">总记录数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">扇区数据</h5>
                                    <h3 class="text-success" id="sectorCount">-</h3>
                                    <small class="text-muted">总记录数</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 删除数据 -->
                    <div class="card">
                        <div class="card-header">
                            <h5>按时间删除数据</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="startDate" onchange="updatePreview()">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endDate" onchange="updatePreview()">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">数据类型</label><br>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="dailyData" value="daily" onchange="updatePreview()">
                                        <label class="form-check-label" for="dailyData">日度</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="monthlyData" value="monthly" onchange="updatePreview()">
                                        <label class="form-check-label" for="monthlyData">月度</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="sectorData" value="sector" onchange="updatePreview()">
                                        <label class="form-check-label" for="sectorData">扇区</label>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label><br>
                                    <button type="button" class="btn btn-danger" onclick="executeDelete()">删除数据</button>
                                </div>
                            </div>

                            <!-- 预览区域 -->
                            <div id="previewArea" class="alert alert-warning mt-3" style="display: none;">
                                <div id="previewContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 查询数据 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5>查询数据</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-2">
                                    <select class="form-select" id="queryTable">
                                        <option value="daily_data">日度数据</option>
                                        <option value="monthly_data">月度数据</option>
                                        <option value="sector_outage_data">扇区数据</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <input type="date" class="form-control" id="queryStartDate">
                                </div>
                                <div class="col-md-3">
                                    <input type="date" class="form-control" id="queryEndDate">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="queryDistrict">
                                        <option value="">全部区县</option>
                                        <option value="巨野县">巨野县</option>
                                        <option value="牡丹区">牡丹区</option>
                                        <option value="鲁西新区">鲁西新区</option>
                                        <option value="单县">单县</option>
                                        <option value="曹县">曹县</option>
                                        <option value="成武县">成武县</option>
                                        <option value="东明县">东明县</option>
                                        <option value="郓城县">郓城县</option>
                                        <option value="鄄城县">鄄城县</option>
                                        <option value="定陶区">定陶区</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-primary" onclick="queryData()">查询</button>
                                </div>
                            </div>
                            
                            <!-- 查询结果 -->
                            <div id="queryResults"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时获取统计信息
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });

        function loadStats() {
            fetch('/api/database_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('dailyCount').textContent = data.stats.daily_count;
                        document.getElementById('monthlyCount').textContent = data.stats.monthly_count;
                        document.getElementById('sectorCount').textContent = data.stats.sector_count;
                    }
                });
        }

        function updatePreview() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const dailyChecked = document.getElementById('dailyData').checked;
            const monthlyChecked = document.getElementById('monthlyData').checked;
            const sectorChecked = document.getElementById('sectorData').checked;

            if (!startDate || !endDate || (!dailyChecked && !monthlyChecked && !sectorChecked)) {
                document.getElementById('previewArea').style.display = 'none';
                return;
            }

            const dataTypes = [];
            if (dailyChecked) dataTypes.push('daily');
            if (monthlyChecked) dataTypes.push('monthly');
            if (sectorChecked) dataTypes.push('sector');

            fetch('/api/delete_preview', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    delete_type: 'date',
                    start_date: startDate,
                    end_date: endDate,
                    data_types: dataTypes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('previewContent').innerHTML = 
                        `将删除 ${startDate} 至 ${endDate} 的 ${dataTypes.join('、')} 数据，共 ${data.total_records} 条记录`;
                    document.getElementById('previewArea').style.display = 'block';
                }
            });
        }

        function executeDelete() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const dataTypes = [];
            if (document.getElementById('dailyData').checked) dataTypes.push('daily');
            if (document.getElementById('monthlyData').checked) dataTypes.push('monthly');
            if (document.getElementById('sectorData').checked) dataTypes.push('sector');

            // 检查必填项
            if (!startDate || !endDate) {
                alert('请选择开始和结束日期');
                return;
            }

            if (dataTypes.length === 0) {
                alert('请选择要删除的数据类型');
                return;
            }

            // 确认删除
            if (!confirm(`确认删除 ${startDate} 至 ${endDate} 的 ${dataTypes.join('、')} 数据吗？`)) {
                return;
            }

            fetch('/api/delete_data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    delete_type: 'date',
                    start_date: startDate,
                    end_date: endDate,
                    data_types: dataTypes,
                    skip_backup: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    document.getElementById('previewArea').style.display = 'none';
                    // 只清空数据类型选择，保留时间选择
                    document.getElementById('dailyData').checked = false;
                    document.getElementById('monthlyData').checked = false;
                    document.getElementById('sectorData').checked = false;
                    loadStats();
                } else {
                    alert('删除失败: ' + data.error);
                }
            });
        }

        function queryData() {
            const table = document.getElementById('queryTable').value;
            const startDate = document.getElementById('queryStartDate').value;
            const endDate = document.getElementById('queryEndDate').value;
            const district = document.getElementById('queryDistrict').value;

            fetch('/api/query_data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    table: table,
                    start_date: startDate,
                    end_date: endDate,
                    district: district
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = `<h6>查询结果 ${data.data.length} 条记录</h6>`;
                    if (data.data.length > 0) {
                        html += '<div class="table-responsive"><table class="table table-sm">';
                        html += '<thead><tr>';
                        Object.keys(data.data[0]).forEach(key => {
                            html += `<th>${key}</th>`;
                        });
                        html += '</tr></thead><tbody>';
                        data.data.forEach(row => {
                            html += '<tr>';
                            Object.values(row).forEach(value => {
                                html += `<td>${value}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody></table></div>';
                    }
                    document.getElementById('queryResults').innerHTML = html;
                }
            });
        }
    </script>
</body>
</html>
