# 重复故障分析扩展方案

## 🎯 **问题1：查看无重复故障的小区**

### **"重复次数"定义说明**
```python
# 当前系统中的定义
occurrence_count = len(history_df)  # 故障发生的天数
repeat_count = int(occurrence_count)  # 重复次数 = 故障天数
```

**重要理解：**
- ✅ **重复次数 = 故障发生的天数**（不是故障总次数）
- ✅ **重复次数=1** = 只在1天发生故障（单次故障）
- ✅ **重复次数=2** = 在2天发生故障（开始重复）
- ✅ **重复次数≥2** = 真正的重复故障

### **查看不同类型故障的设置**
```
查看单次故障小区：设置最小重复次数为 1
查看重复故障小区：设置最小重复次数为 2 或更高
查看严重重复故障：设置最小重复次数为 5 或更高
```

## 🚀 **问题2：扩展时间维度分析**

### **技术可行性：✅ 完全可行**

当前系统已具备所有技术基础：
- ✅ 后端支持灵活时间范围查询
- ✅ 前端有模式切换机制
- ✅ 数据库查询逻辑已参数化
- ✅ Excel导出支持动态标题

### **建议的用户界面设计**

#### **分析模式选择器**
```html
<div class="col-md-4 mb-3">
    <label for="analysisMode" class="form-label">分析模式</label>
    <select class="form-select" id="analysisMode" name="analysis_mode">
        <option value="auto" selected>🧠 智能分析（全历史数据）</option>
        <option value="week">📅 本周分析（最近7天）</option>
        <option value="month">📊 本月分析（当月1日至今）</option>
        <option value="manual">⚙️ 自定义分析（手动指定）</option>
    </select>
    <small class="form-text text-muted">选择分析的时间范围</small>
</div>
```

#### **动态说明信息**
```html
<!-- 智能分析说明 -->
<div id="autoAnalysisInfo" class="alert alert-success">
    <i class="bi bi-cpu"></i>
    <strong>智能分析：</strong>使用数据库中的全部历史数据进行分析，结果最全面准确
</div>

<!-- 本周分析说明 -->
<div id="weekAnalysisInfo" class="alert alert-info" style="display: none;">
    <i class="bi bi-calendar-week"></i>
    <strong>本周分析：</strong>分析最近7天的重复故障情况，适合短期趋势观察
</div>

<!-- 本月分析说明 -->
<div id="monthAnalysisInfo" class="alert alert-warning" style="display: none;">
    <i class="bi bi-calendar-month"></i>
    <strong>本月分析：</strong>分析当月1日至今的重复故障情况，适合月度考核
</div>

<!-- 自定义分析说明 -->
<div id="manualAnalysisInfo" class="alert alert-secondary" style="display: none;">
    <i class="bi bi-gear"></i>
    <strong>自定义分析：</strong>手动指定分析天数，灵活控制分析范围
</div>
```

#### **时间范围预览**
```html
<div class="col-12 mb-3">
    <div class="card">
        <div class="card-body">
            <h6 class="card-title">📅 分析时间范围预览</h6>
            <div id="timeRangePreview" class="text-muted">
                将根据选择的模式自动计算时间范围
            </div>
        </div>
    </div>
</div>
```

### **前端JavaScript扩展**

#### **模式切换逻辑**
```javascript
function toggleAnalysisMode() {
    const mode = document.getElementById('analysisMode').value;
    const targetDate = document.getElementById('targetDate').value || getTodayDate();
    
    // 隐藏所有说明
    hideAllAnalysisInfo();
    
    // 显示对应说明和控件
    switch(mode) {
        case 'auto':
            document.getElementById('autoAnalysisInfo').style.display = 'block';
            document.getElementById('analyzeBtn').innerHTML = '<i class="bi bi-cpu"></i> 开始智能分析';
            updateTimeRangePreview('全部历史数据', targetDate);
            break;
            
        case 'week':
            document.getElementById('weekAnalysisInfo').style.display = 'block';
            document.getElementById('analyzeBtn').innerHTML = '<i class="bi bi-calendar-week"></i> 开始本周分析';
            updateTimeRangePreview('最近7天', targetDate);
            break;
            
        case 'month':
            document.getElementById('monthAnalysisInfo').style.display = 'block';
            document.getElementById('analyzeBtn').innerHTML = '<i class="bi bi-calendar-month"></i> 开始本月分析';
            updateTimeRangePreview('当月1日至今', targetDate);
            break;
            
        case 'manual':
            document.getElementById('manualAnalysisInfo').style.display = 'block';
            document.getElementById('manualDaysRow').style.display = 'block';
            document.getElementById('analyzeBtn').innerHTML = '<i class="bi bi-gear"></i> 开始自定义分析';
            updateTimeRangePreview('自定义天数', targetDate);
            break;
    }
}

function updateTimeRangePreview(mode, targetDate) {
    const preview = document.getElementById('timeRangePreview');
    const target = new Date(targetDate);
    
    let startDate, endDate, description;
    
    switch(mode) {
        case '全部历史数据':
            description = `从数据库最早日期到 ${targetDate}`;
            break;
            
        case '最近7天':
            startDate = new Date(target.getTime() - 6 * 24 * 60 * 60 * 1000);
            description = `${formatDate(startDate)} 到 ${targetDate} (共7天)`;
            break;
            
        case '当月1日至今':
            startDate = new Date(target.getFullYear(), target.getMonth(), 1);
            const days = Math.ceil((target - startDate) / (24 * 60 * 60 * 1000)) + 1;
            description = `${formatDate(startDate)} 到 ${targetDate} (共${days}天)`;
            break;
            
        case '自定义天数':
            const daysBack = document.getElementById('daysBack').value || 30;
            startDate = new Date(target.getTime() - (daysBack - 1) * 24 * 60 * 60 * 1000);
            description = `${formatDate(startDate)} 到 ${targetDate} (共${daysBack}天)`;
            break;
    }
    
    preview.innerHTML = `<i class="bi bi-clock"></i> ${description}`;
}
```

### **后端API扩展**

#### **分析模式处理**
```python
def analyze_repeated_outages_api(target_date, days_back, min_occurrences, analysis_mode):
    """扩展的重复故障分析API"""
    
    # 计算起始日期
    target_dt = datetime.strptime(target_date, '%Y-%m-%d')
    
    if analysis_mode == 'auto':
        # 智能分析：全部历史数据
        cursor.execute("SELECT MIN(date) FROM sector_outage_data")
        start_date = cursor.fetchone()[0]
        mode_desc = "智能分析（全历史）"
        
    elif analysis_mode == 'week':
        # 本周分析：最近7天
        start_dt = target_dt - timedelta(days=6)
        start_date = start_dt.strftime('%Y-%m-%d')
        mode_desc = "本周分析（7天）"
        
    elif analysis_mode == 'month':
        # 本月分析：当月1日至今
        start_dt = target_dt.replace(day=1)
        start_date = start_dt.strftime('%Y-%m-%d')
        mode_desc = "本月分析"
        
    elif analysis_mode == 'manual':
        # 自定义分析：手动指定天数
        start_dt = target_dt - timedelta(days=days_back-1)
        start_date = start_dt.strftime('%Y-%m-%d')
        mode_desc = f"自定义分析（{days_back}天）"
    
    # 计算实际天数
    actual_days = (target_dt - datetime.strptime(start_date, '%Y-%m-%d')).days + 1
    
    # 返回分析信息
    analysis_info = {
        'target_date': target_date,
        'start_date': start_date,
        'actual_days': actual_days,
        'analysis_mode': analysis_mode,
        'mode_description': mode_desc,
        'min_occurrences': min_occurrences
    }
    
    # ... 继续原有的分析逻辑
```

### **Excel导出扩展**

#### **动态标题生成**
```python
def generate_excel_title(analysis_info):
    """生成动态Excel标题"""
    mode_names = {
        'auto': '智能分析（全历史）',
        'week': '本周分析（7天）',
        'month': '本月分析',
        'manual': '自定义分析'
    }
    
    mode_name = mode_names.get(analysis_info['analysis_mode'], '未知模式')
    
    return f"重复故障分析报告 - {analysis_info['target_date']} ({mode_name})"

def generate_excel_info(analysis_info):
    """生成动态Excel信息行"""
    return f"分析范围: {analysis_info['start_date']} 至 {analysis_info['target_date']} " \
           f"(共{analysis_info['actual_days']}天) | " \
           f"分析模式: {analysis_info['mode_description']} | " \
           f"最小重复: {analysis_info['min_occurrences']}次"
```

### **实施优先级建议**

#### **第一阶段（高优先级）**
1. ✅ 添加"本周分析"模式
2. ✅ 添加"本月分析"模式
3. ✅ 修改前端界面和交互逻辑
4. ✅ 扩展后端API处理逻辑

#### **第二阶段（中优先级）**
1. ✅ 添加时间范围预览功能
2. ✅ 优化用户界面体验
3. ✅ 添加更多分析模式（季度、年度等）

#### **第三阶段（低优先级）**
1. ✅ 添加自定义日期范围选择器
2. ✅ 支持多时间段对比分析
3. ✅ 集成更多可视化功能

### **预期用户体验**

#### **使用场景**
```
📅 本周分析：
- 用途：周例会汇报，快速了解本周重复故障情况
- 优势：数据及时，便于短期决策

📊 本月分析：
- 用途：月度考核，评估当月维护效果
- 优势：符合管理周期，便于绩效评估

🧠 智能分析：
- 用途：全面分析，识别长期重复故障模式
- 优势：数据全面，分析结果最准确

⚙️ 自定义分析：
- 用途：特殊需求，如节假日期间、特定事件后
- 优势：灵活可控，满足个性化需求
```

#### **操作流程**
```
1. 选择分析模式 → 系统显示对应说明和时间范围预览
2. 设置目标日期 → 系统自动更新时间范围预览
3. 设置最小重复次数 → 根据需要调整筛选条件
4. 点击分析按钮 → 系统执行对应模式的分析
5. 查看结果 → 导出Excel或发送邮件
```

---

**技术可行性**: ✅ 完全可行  
**开发复杂度**: 🟡 中等（主要是界面和逻辑扩展）  
**用户价值**: 🟢 高（满足不同时间维度的分析需求）  
**实施建议**: 分阶段实施，优先实现本周和本月分析  

扩展时间维度分析功能将大大提升重复故障分析的实用性和灵活性！🎉
