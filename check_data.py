import sqlite3

def check_sector_data():
    """检查扇区数据"""
    try:
        conn = sqlite3.connect('heze_data.db')
        cursor = conn.cursor()
        
        print("=== 检查扇区数据表结构 ===")
        cursor.execute("PRAGMA table_info(sector_outage_data)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"{col[1]}: {col[2]}")
        
        print("\n=== 检查扇区数据日期分布 ===")
        cursor.execute("SELECT date, COUNT(*), SUM(outage_count) FROM sector_outage_data GROUP BY date ORDER BY date")
        date_results = cursor.fetchall()
        for r in date_results:
            print(f"日期: {r[0]}, 记录数: {r[1]}, 总退服: {r[2]}")
        
        print("\n=== 检查2025-08-05的扇区数据 ===")
        cursor.execute("SELECT grid, COUNT(*), SUM(outage_count) FROM sector_outage_data WHERE date = '2025-08-05' GROUP BY grid ORDER BY grid")
        grid_results = cursor.fetchall()
        total_outages = 0
        for r in grid_results:
            print(f"网格: {r[0]}, 记录数: {r[1]}, 总退服: {r[2]}")
            total_outages += r[2] if r[2] else 0
        
        print(f"\n2025-08-05 总退服次数: {total_outages}")
        
        print("\n=== 检查日度数据对比 ===")
        cursor.execute("SELECT district, total_outages FROM daily_data WHERE date = '2025-08-05' ORDER BY district")
        daily_results = cursor.fetchall()
        for r in daily_results:
            print(f"日度数据 - 网格: {r[0]}, 累计退服: {r[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据失败: {e}")

if __name__ == "__main__":
    check_sector_data()
